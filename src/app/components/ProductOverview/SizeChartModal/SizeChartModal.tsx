import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { Product } from "types/product.type"

import SizeChartModalContent from "./SizeChartModalContent"

const { SIZE_CHART } = ModalConstant.MODAL_IDS

const SizeChartModal = ({ item }: { item: Product }) => {
  const { open, modalId, setOpen } = useModalStore()
  const isOpen = open && modalId === SIZE_CHART

  if (!isOpen) return null

  return (
    <Modal modalId={SIZE_CHART}>
      <HeaderModal
        onClose={() => setOpen(false, SIZE_CHART)}
        title="Size Chart"
      />
      <SizeChartModalContent item={item} />
    </Modal>
  )
}

export default SizeChartModal
