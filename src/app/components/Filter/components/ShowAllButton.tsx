import React from "react"
import { But<PERSON> } from "@kickavenue/ui/components"

interface ShowAllButtonProps {
  filteredItems: any[]
  maxItems: number
  showAll: boolean
  toggleShowAll: () => void
}

export const ShowAllButton: React.FC<ShowAllButtonProps> = ({
  filteredItems,
  maxItems,
  showAll,
  toggleShowAll,
}) => {
  if (filteredItems.length <= maxItems) return null

  const textShowAll = showAll ? "Show Less" : "Show All"

  return (
    <Button
      variant="link"
      onClick={toggleShowAll}
      className="w-full justify-center"
    >
      {textShowAll}
    </Button>
  )
}
