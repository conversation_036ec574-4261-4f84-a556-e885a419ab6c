import { Text } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"

import { TListingItem } from "types/listingItem.type"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatCurrency } from "@utils/separator"
import { TTableColumn } from "types/table.type"
import BulkActionTable from "@components/shared/BulkActionTable"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { formatPriceMinUnitVal } from "@utils/misc"

const width = {
  consignmentId: 152,
  productDetail: 224,
  SKU: 108,
  size: 64,
  listingPrice: 148,
  expiryDate: 140,
}

const ConsignmentTakeOutTable = () => {
  const { consignmentAllData, selectedRowKeys } = useConsignmentActiveStore()
  const columns = [
    {
      key: "consignmentId",
      title: "Consignment ID",
      headerClassName: styles["sticky-header-1"],
      contentClassName: styles["sticky-content-1"],
      width: width.consignmentId,
      render: (record: TListingItem) => {
        return (
          <Text size="sm" state="primary" type="regular">
            {record.consignmentId}
          </Text>
        )
      },
      sorter: () => {},
    },
    {
      key: "productDetails",
      title: "Product Name",
      width: width.productDetail,
      headerClassName: cx(styles["sticky-header-2"], "!left-[214px]"),
      contentClassName: cx(styles["sticky-content-2"], "!left-[214px]"),
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.name?.localeCompare(b?.item?.name),
      defaultSortOrder: "ascend",
      render: (record: TListingItem) => <>{record?.item?.name}</>,
    },
    {
      key: "SKU",
      title: "SKU",
      width: width.SKU,
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.item?.skuCode?.localeCompare(b?.item?.skuCode),
      render: (record: TListingItem) => <>{record?.item?.skuCode}</>,
    },
    {
      key: "size",
      title: "Size",
      width: width.size,
      sorter: (a: TListingItem, b: TListingItem) =>
        a.size?.us?.localeCompare(b.size?.us),
      render: (record: TListingItem) => <>{`US ${record.size?.us}`}</>,
    },
    {
      key: "listingPrice",
      title: "Listing Price",
      headerClassName: "[&>div]:justify-end",
      contentClassName: "text-right",
      width: width.listingPrice,
      render: (record: TListingItem) =>
        formatCurrency(
          formatPriceMinUnitVal(record?.sellingPrice?.minUnitVal || 0) || 0,
          ",",
          "IDR",
        ),
      sorter: (a: TListingItem, b: TListingItem) =>
        Number(formatPriceMinUnitVal(a?.sellingPrice?.minUnitVal || 0)) -
        formatPriceMinUnitVal(b?.sellingPrice?.minUnitVal || 0),
    },
    {
      key: "expiryDate",
      title: "Expiry Date",
      width: width.expiryDate,
      sorter: (a: TListingItem, b: TListingItem) =>
        a?.expiryDate?.localeCompare(b?.expiryDate),
      render: (record: TListingItem) => <>{record?.expiryDate}</>,
    },
  ] as TTableColumn[]

  return (
    <BulkActionTable
      columns={columns}
      data={consignmentAllData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default ConsignmentTakeOutTable
