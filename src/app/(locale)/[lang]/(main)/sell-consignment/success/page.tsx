"use client"

import React, { useEffect } from "react"
import {
  But<PERSON>,
  Head<PERSON>,
  IconSuccessCircleBulk,
  Text,
} from "@kickavenue/ui/dist/src/components"
import { useRouter, useSearchParams } from "next/navigation"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useSellConsignmentStore } from "stores/sellConsignmentStore"
import { useSellerListingStore } from "stores/useSellerListing"
import { useFormSellConsignmentStore } from "stores/formSellConsignmentStore"
import { useSizeStore } from "@stores/useSizeStore"

export default function SellingSuccessPage() {
  const router = useRouter()
  const resetSellConsignment = useSellConsignmentStore(
    (state) => state.resetState,
  )
  const resetSellerListing = useSellerListingStore((state) => state.resetState)
  const resetFormSellConsignment = useFormSellConsignmentStore(
    (state) => state.resetState,
  )
  const { resetState: resetSizeStore } = useSizeStore()
  const searchParams = useSearchParams()

  const isConsignment = searchParams.get("consignment")

  useEffect(() => {
    // Reset all states when component mounts
    const resetAllStates = () => {
      resetSellConsignment()
      resetSellerListing()
      resetFormSellConsignment()
      resetSizeStore()
    }

    resetAllStates()

    // Handle navigation back to sell-consignment
    const handlePopState = () => {
      resetAllStates()
      router.replace("/sell-consignment")
    }

    window.addEventListener("popstate", handlePopState)

    // Clear browser history and replace with success page
    window.history.replaceState(
      { from: "success" },
      "",
      `/sell-consignment/success${isConsignment ? "?consignment=true" : ""}`,
    )

    return () => {
      window.removeEventListener("popstate", handlePopState)
      resetAllStates()
    }
  }, [
    resetSellConsignment,
    resetSellerListing,
    resetFormSellConsignment,
    resetSizeStore,
    isConsignment,
    router,
  ])

  const handleViewListings = () => {
    router.push(
      `/profile/${isConsignment ? "consignment" : "selling"}?page=0&pageSize=10`,
    )
  }

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="flex h-svh flex-col items-center justify-center gap-lg bg-white py-xxl">
        <IconSuccessCircleBulk className="size-[29px] scale-[2.2] text-success" />
        <Heading heading="4" textStyle="bold" className="text-center">
          Your Selling Request has been Submitted!
        </Heading>
        <Text size="base" state="secondary" type="regular">
          Remember to stay authentic!
        </Text>
        <Button onClick={handleViewListings}>View Your Listings</Button>
      </div>
    </AuthRedirectWrapper>
  )
}
