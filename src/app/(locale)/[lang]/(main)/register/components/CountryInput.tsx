import { Label } from "@kickavenue/ui/components"
import debounce from "lodash/debounce"
import { useCallback, useMemo, useState } from "react"

import InputCountry from "@app/components/InputCountry"
import { useCountries } from "@application/hooks/useCountries"
import { useRegistrationStore } from "@stores/registrationStore"

export function CountryInput(): JSX.Element {
  const { registrationData, setRegistrationData } = useRegistrationStore()
  const [searchQuery, setSearchQuery] = useState("")

  const { data: countries, refetch } = useCountries({
    name: searchQuery || undefined,
  })

  const filteredCountries = useMemo(
    () =>
      countries?.filter(
        (country) => country.name && country.country && country.flag,
      ) || [],
    [countries],
  )

  const handleSearch = useCallback(
    (query: string) => {
      const debouncedSearch = debounce(() => {
        setSearchQuery(query)
        refetch()
      }, 300)
      debouncedSearch()
    },
    [refetch],
  )

  return (
    <div className="mt-4 flex flex-col gap-y-xs">
      <Label state="required" size="sm" type="default">
        Country
      </Label>
      <InputCountry
        countries={filteredCountries}
        selectedCountry={
          filteredCountries?.find(
            (c) => c.country === registrationData.countryId,
          ) || null
        }
        setSelectedCountry={(country) =>
          setRegistrationData({ countryId: country?.country || 0 })
        }
        onSearch={handleSearch}
      />
    </div>
  )
}
