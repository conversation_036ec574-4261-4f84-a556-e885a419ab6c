"use client"

import React, { useState } from "react"
import {
  Input,
  IconAddOutline,
  IconMinusOutline,
} from "@kickavenue/ui/components"

interface QuantityInputProps {
  initialValue?: number
  min?: number
  max?: number
  onChange?: (value: number) => void
  disabled?: boolean
  [key: string]: any
}

const QuantityInput: React.FC<QuantityInputProps> = ({
  initialValue = 0,
  min = 0,
  max = Infinity,
  onChange,
  disabled = false,
  ...restProps
}) => {
  const [quantity, setQuantity] = useState(initialValue)

  const handleIncrement = () => {
    if (quantity >= max) return
    const newQuantity = quantity + 1
    setQuantity(newQuantity)
    onChange?.(newQuantity)
  }

  const handleDecrement = () => {
    if (quantity <= min) return
    const newQuantity = quantity - 1
    setQuantity(newQuantity)
    onChange?.(newQuantity)
  }

  const DecrementButton = () => (
    <button
      onClick={handleDecrement}
      className=""
      disabled={quantity <= min || disabled}
      type="button"
      aria-label="Decrease quantity"
    >
      <IconMinusOutline />
    </button>
  )

  const IncrementButton = () => (
    <button
      onClick={handleIncrement}
      className="font-medium"
      disabled={quantity >= max || disabled}
      type="button"
      aria-label="Increase quantity"
    >
      <IconAddOutline />
    </button>
  )

  return (
    <Input
      {...restProps}
      value={quantity}
      readOnly
      leftIcon={<DecrementButton />}
      rightIcon={<IncrementButton />}
      style={{ textAlign: "center", fontWeight: 500, fontSize: 14 }}
      disabled={disabled}
    />
  )
}

export default QuantityInput
