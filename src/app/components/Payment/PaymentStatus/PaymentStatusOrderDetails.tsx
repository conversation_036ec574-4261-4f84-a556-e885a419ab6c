import Text from "@kickavenue/ui/components/Text"
import { useState } from "react"
import {
  IconArrowDownOutline,
  IconArrowUpOutline,
} from "@kickavenue/ui/components/icons"

import SkeletonText from "@components/shared/Skeleton/SkeletonText"
import { TTransaction } from "types/transaction.type"
import { TTransactionDetail } from "types/transactionDetail.type"
import ClickableDiv from "@components/shared/ClickableDiv"
import TooltipText from "@components/shared/TooltipText"
import useWindowSize from "@app/hooks/useWindowSize"
import { isEmpty } from "@utils/misc"
import CopyButton from "@components/shared/CopyButton"

import { getIconFromFinanceProvider } from "../payment.utils"

const PaymentStatusOrderDetails = ({
  transaction,
  txDetails,
  isLoadingTxDetails,
}: {
  transaction?: TTransaction | null
  txDetails?: TTransactionDetail[] | null
  isLoadingTxDetails?: boolean
}) => {
  const [showSubitems, setShowSubitems] = useState(false)
  const { isMobile } = useWindowSize()

  const orderIdMaxLength = isMobile ? 20 : 42
  const productNameMaxLength = isMobile ? 20 : 62

  const va = transaction?.paymentData?.paymentMethod?.virtualAccountNumber
  const bankName = transaction?.paymentData?.paymentMethod?.bankName

  if (isLoadingTxDetails) {
    return (
      <div className="w-full rounded-base border border-gray-w-80">
        <div className="flex flex-col gap-xs p-base">
          <SkeletonText className="w-full" />
        </div>
      </div>
    )
  }

  return (
    <div className="w-full rounded-base border border-gray-w-80">
      <div className="flex flex-col gap-xs p-base pt-sm">
        <div className="flex flex-col gap-xs">
          {!isEmpty(va) && (
            <div className="flex justify-between">
              <Text
                size="sm"
                type="regular"
                state="primary"
                className="flex items-center"
              >
                Virtual Account No.
              </Text>
              <div className="flex items-center gap-xxs">
                {getIconFromFinanceProvider(bankName || "")}
                <Text size="sm" type="bold" state="primary">
                  {va || "-"}
                </Text>
                {va && <CopyButton text={va} />}
              </div>
            </div>
          )}
          <div className="flex justify-between">
            <Text
              size="sm"
              type="regular"
              state="primary"
              className="flex items-center gap-xxs"
            >
              Order ID
              <ClickableDiv
                onClick={() => setShowSubitems(!showSubitems)}
                keyDownHandler={() => setShowSubitems(!showSubitems)}
              >
                {showSubitems && <IconArrowUpOutline />}
                {!showSubitems && <IconArrowDownOutline />}
              </ClickableDiv>
            </Text>
            <TooltipText
              text={transaction?.orderId || "-"}
              textProps={{ type: "bold", state: "primary", size: "sm" }}
              maxLength={orderIdMaxLength}
            />
          </div>
          {showSubitems &&
            txDetails?.map((txDetail) => (
              <div key={txDetail.id} className="mx-xs flex justify-between">
                <TooltipText
                  text={txDetail?.listingItem?.item?.name || "-"}
                  textProps={{
                    type: "regular",
                    state: "secondary",
                    size: "sm",
                  }}
                  maxLength={productNameMaxLength}
                />
                <Text size="sm" type="regular" state="primary">
                  {txDetail?.invoiceNumber || "-"}
                </Text>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}

export default PaymentStatusOrderDetails
