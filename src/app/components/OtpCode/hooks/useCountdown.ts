import { useState, useEffect } from "react"

const useCountdown = (initialTime: number) => {
  const [remainingTime, setRemainingTime] = useState<number>(0)

  const startCountdown = () => {
    setRemainingTime(initialTime)
  }

  useEffect(() => {
    if (remainingTime <= 0) {
      return
    }

    const timer = setTimeout(() => setRemainingTime(remainingTime - 1), 1000)
    return () => clearTimeout(timer)
  }, [remainingTime])

  return { remainingTime, startCountdown }
}

export default useCountdown
