"use client"

import { Tab, But<PERSON> } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"

interface TabBuyingProps {
  selectedTab: string
  onTabChange: (tab: string) => void
}

export default function TabBuying({
  selectedTab,
  onTabChange,
}: TabBuyingProps) {
  const tabNames = ["Offers", "Pending", "In Progress", "History"]

  return (
    <div className="">
      <Tab className="mt-8 !w-full !gap-x-xl !pb-3 lg:!justify-start">
        {tabNames.map((item) => {
          const isActive = item === selectedTab
          return (
            <Button
              data-active={isActive}
              onClick={() => onTabChange(item)}
              variant="link"
              key={item}
              className={cx(
                "whitespace-nowrap !p-0",
                isActive
                  ? "border-b-2 border-gray-b-65 !font-bold !text-gray-b-65"
                  : "!font-normal !text-gray",
              )}
            >
              {item}
            </Button>
          )
        })}
      </Tab>
    </div>
  )
}
