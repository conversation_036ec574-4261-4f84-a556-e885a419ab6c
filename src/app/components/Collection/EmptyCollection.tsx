import Empty from "@kickavenue/ui/components/Empty"
import { Button } from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"

import CenterWrapper from "@components/shared/CenterWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"

const EmptyCollection = () => {
  const router = useRouter()

  return (
    <CenterWrapper>
      <div className="col-span-4 flex justify-center md:col-span-12">
        <Empty
          title="No Collection Found"
          subText="Try looking for another collection"
          actionButton={
            <Button
              size="md"
              variant="primary"
              onClick={() => router.push(PageRouteConstant.SEARCH)}
            >
              Explore More
            </Button>
          }
          className="[&>img]:size-[205px]"
        />
      </div>
    </CenterWrapper>
  )
}

export default EmptyCollection
