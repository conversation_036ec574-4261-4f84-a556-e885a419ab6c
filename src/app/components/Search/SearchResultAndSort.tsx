import { Text } from "@kickavenue/ui/components"

import useSearchFilter from "@hooks/useSearchFilter"
import { useSearchStore } from "stores/searchStore"

import SearchSortBy from "./SearchSortBy"

const SearchResultAndSort = () => {
  const { filter } = useSearchFilter()
  const { totalSize } = useSearchStore()
  const renderKeyword = filter?.keyword ? (
    <>
      <Text size="base" type="regular" state="primary">
        results
      </Text>
      <Text size="base" type="regular" state="primary">
        for
      </Text>
      <Text size="base" type="bold" state="primary">
        &quot;{filter?.keyword}&quot;
      </Text>
    </>
  ) : null
  return (
    <div className="flex justify-between">
      <div className="flex gap-xxs">
        <Text size="base" type="regular" state="primary">
          Found
        </Text>
        <Text size="base" type="bold" state="primary">
          {totalSize || 0}
        </Text>
        {renderKeyword}
      </div>
      <div className="flex justify-end">
        <SearchSortBy />
      </div>
    </div>
  )
}

export default SearchResultAndSort
