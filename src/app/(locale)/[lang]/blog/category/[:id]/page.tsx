"use client"

import { Pagination } from "@kickavenue/ui/dist/src/components"

import BannerImageCategory from "./component/BannerImageCategory"
import ListComponentBlog from "./component/ListComponentBlog"

const CategoryAll = () => {
  return (
    <>
      <BannerImageCategory />
      <ListComponentBlog />
      <ListComponentBlog />
      <ListComponentBlog />
      <ListComponentBlog />
      <div className="my-8 flex">
        <Pagination
          currentPage={1}
          totalPages={10}
          onNext={() => {}}
          onPrev={() => {}}
          onPage={() => {}}
          isNextDisable={false}
          isPrevDisable={false}
          size="sm"
          className="mx-auto"
        />
      </div>
    </>
  )
}

export default CategoryAll
