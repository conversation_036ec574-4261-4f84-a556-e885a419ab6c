import { Divider, Select } from "@kickavenue/ui/dist/src/components"
import { useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"
import { useSearchParams } from "next/navigation"
import compact from "lodash/compact"

import Spinner from "@components/shared/Spinner"
import useFetchUniqueSize from "@app/hooks/useFetchUniqueSize"
import { getUniqueSizeIds } from "@utils/sizeChart.utils"
import useURLQuery from "@app/hooks/useUrlQuery"
import { TUniqueSize } from "types/sizeChart.type"
import { TProductFilterKey } from "types/product.type"
import { event } from "@lib/gtag"

import { SizeButton } from "./FilterItem"
import { FilterHeader } from "./FilterHeader"

const { SizeId, Size } = TProductFilterKey

// eslint-disable-next-line max-lines-per-function
const FilterSize = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [sizeSystem, setSizeSystem] = useState<string>("US")
  const sizeSystemOptions = ["US", "EU", "UK", "CM"]
  const { handleBulkChangeQuery } = useURLQuery()
  const searchParams = useSearchParams()
  const { ref, inView } = useInView()
  const sizes = searchParams.get("size")

  const {
    data: uniqueSize,
    hasNextPage,
    isFetching,
    fetchNextPage,
  } = useFetchUniqueSize()

  const isSizeSelected = (size: TUniqueSize) => {
    const sizeIds = searchParams?.get(SizeId)
    return Boolean(sizeIds?.includes(size.id?.join(",")))
  }

  const isSizeUsSelected = (size: TUniqueSize) => {
    const sizeUs = searchParams?.get(Size)
    return Boolean(sizeUs?.includes(size.us))
  }

  const handleClickSize = (size: TUniqueSize) => {
    const prevSizeIds = searchParams?.get(SizeId) || ""
    const prevSizeUs = searchParams?.get(Size) || ""

    const newSizeIds = isSizeSelected(size)
      ? prevSizeIds?.split(",").filter((id) => id !== size.id?.join(","))
      : [...prevSizeIds.split(","), size.id?.join(",")]

    const newSizeUs = isSizeUsSelected(size)
      ? prevSizeUs?.split(",").filter((us) => us !== size.us)
      : [...prevSizeUs.split(","), size.us]

    handleBulkChangeQuery({
      [SizeId]: compact(newSizeIds).join(","),
      [Size]: compact(newSizeUs).join(","),
    })
  }

  useEffect(() => {
    if (inView && !isFetching) {
      fetchNextPage()
    }
  }, [inView, isFetching, fetchNextPage])

  return (
    <>
      <FilterHeader
        title="Size"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
      />
      {isOpen && (
        <>
          <Select
            value={sizeSystem}
            options={sizeSystemOptions}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
              setSizeSystem(e.target.value)
            }
          >
            {sizeSystemOptions.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </Select>
          <div className="max-h-[489px] overflow-y-auto">
            <div className="mt-4 grid grid-cols-4 gap-3">
              {uniqueSize?.pages.map((page) =>
                page.content.map((item) => (
                  <SizeButton
                    key={getUniqueSizeIds(item)}
                    size={item.us}
                    isSelected={isSizeSelected(item)}
                    onClick={() => {
                      const str = sizes ? sizes.replace(/,/g, "|") : ""
                      const output = str
                        .split(",")
                        .map((part) => `${part.trim()} us`)
                        .join("|")
                      event({
                        action: "product_searched",
                        params: {
                          size: `${item.us} us | ${sizes ? output : ""}`,
                        },
                      })
                      handleClickSize(item)
                    }}
                  />
                )),
              )}
            </div>
            {hasNextPage && (
              <div ref={ref} className="flex justify-center">
                <Spinner className="my-2" />
              </div>
            )}
          </div>
        </>
      )}
      <Divider orientation="horizontal" className="!mt-4" />
    </>
  )
}

export default FilterSize
