import { useBuyingInProgressStore } from "stores/buyingInProgressStore"
import { TTransactionDetailFilter } from "types/transactionDetail.type"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { camelToSnake, isEmpty, isEmptyArray } from "@utils/misc"
import SellingTableEmpty from "@components/SellingDashboard/SellingTableEmpty"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import Table from "@components/shared/Table"
import useFetchMyTxDetails from "@app/hooks/useFetchMyTxDetails"
import TablePagination from "@components/shared/Table/TablePagination"
import { isQueryLoading } from "@utils/network"
import { TSorter } from "types/table.type"
import useURLQuery from "@app/hooks/useUrlQuery"

import { BuyingDashboardConstant } from "../buyingDashboard.contant"

import useBuyingInProgressPagination from "./hooks/useBuyingInProgressPagination"
import useBuyingInProgressTable from "./hooks/useBuyingInProgressTable"
import styles from "./BuyingDashboardInProgressTable.module.scss"

const { SORT_BY } = BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardInProgressTable = () => {
  const { list, filter, setList, setFilter } = useBuyingInProgressStore()
  const { handleChangeQuery } = useURLQuery()

  const { columns } = useBuyingInProgressTable()

  const paging = useBuyingInProgressPagination()

  const txQuery = useFetchMyTxDetails({
    onSuccess: (data) => {
      setList(data?.content)
      setFilter({
        ...((filter || {}) as TTransactionDetailFilter),
        totalPages: data?.totalPages,
      })
    },
    filters: filter as TTransactionDetailFilter,
    enabled:
      !isEmpty(filter?.page) &&
      !isEmpty(filter?.pageSize) &&
      !isEmptyArray(filter?.status) &&
      !isEmpty(filter?.sortBy),
  })

  const isLoading = isQueryLoading(txQuery)

  const handleTableChange = (sorter: TSorter) => {
    const field = camelToSnake(sorter.field)?.toLowerCase()
    handleChangeQuery(SORT_BY, `${field},${sorter.order}`)
  }

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(100vh-400px)]" />
  }

  if (!isLoading && isEmptyArray(list)) {
    return (
      <SellingTableEmpty
        title="No Active Purchases"
        subText="Your buying journey is underway. No active purchases yet. Explore our collection and start your next purchase adventure."
        pageLink={PageRouteConstant.SEARCH}
        btnText="Explore Collection"
      />
    )
  }

  return (
    <>
      <div className={styles["table-container"]}>
        <Table
          rowKey="transactionId"
          columns={columns}
          dataSource={list}
          className={styles.table}
          onTableChange={handleTableChange}
          activeSort={filter?.sortBy}
        />
      </div>

      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default BuyingDashboardInProgressTable
