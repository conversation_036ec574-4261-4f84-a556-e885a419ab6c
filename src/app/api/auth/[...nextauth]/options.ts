/* eslint-disable @typescript-eslint/naming-convention */

import NextAuth, { NextAuthOptions } from "next-auth"
import CognitoProvider from "next-auth/providers/cognito"
import CredentialsProvider from "next-auth/providers/credentials"

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === "development",
  session: {
    strategy: "jwt",
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
        accessToken: { label: "Access Token", type: "text" },
        refreshToken: { label: "Refresh Token", type: "text" },
        accessTokenExpires: { label: "Access Token Expiration", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.accessToken) {
          throw new Error("Invalid credentials")
        }
        return {
          id: credentials.email,
          email: credentials.email,
          name: credentials.email.split("@")[0],
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          accessTokenExpires: parseInt(credentials.accessTokenExpires, 10),
        }
      },
    }),
    CognitoProvider({
      clientId: process.env.COGNITO_CLIENT_ID || "",
      clientSecret: process.env.COGNITO_CLIENT_SECRET || "",
      issuer: process.env.COGNITO_ISSUER,
      idToken: true,
      checks: ["state", "nonce"],
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (account?.provider === "cognito" && account?.access_token) {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/verify-cognito-token`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              provider: "google",
              token: account?.id_token,
            }),
          },
        )

        const data = await res.json()
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.accessToken = data.Data.access_token
        token.refreshToken = data.Data.refresh_token
        token.accessTokenExpires = user.accessTokenExpires
        return token
      }
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.accessToken = user.accessToken
        token.refreshToken = user.refreshToken
        token.accessTokenExpires = user.accessTokenExpires
      }
      return token
    },
    async session({ session, token }) {
      session.user = {
        ...session.user,
        id: token.id,
        email: token.email,
        name: token.name,
        accessToken: token.accessToken,
        refreshToken: token.refreshToken,
        accessTokenExpires: token.accessTokenExpires,
      }
      return session
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
}

export default NextAuth(authOptions)
