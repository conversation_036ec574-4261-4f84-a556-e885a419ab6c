"use client"

import {
  BadgeCount,
  Breadcrumb,
  IconFilterOutline,
} from "@kickavenue/ui/components"

import SlashSeperator from "@shared/SlashSeparator"
import CenterWrapper from "@shared/CenterWrapper"
import { useMiscStore } from "stores/miscStore"
import useSearchFilter from "@hooks/useSearchFilter"

const SearchBreadCrumb = () => {
  const { setShowFilterDrawer } = useMiscStore()
  const { filter, filterCount } = useSearchFilter()
  const list = [
    { name: "Home", path: "/" },
    { name: "Search", path: "/search" },
  ]
  if (filter.keyword) {
    list.push({ name: filter.keyword, path: "" })
  }
  const renderBadgeCount = () => {
    if (!filterCount) {
      return null
    }
    return (
      <BadgeCount
        type="information"
        count={filterCount}
        size="md"
        className="absolute right-[7px] top-[7px]"
      />
    )
  }
  return (
    <div className="relative pt-base md:pt-xl">
      <CenterWrapper className="items-center !py-0">
        <div className="col-span-2 md:col-span-6">
          <Breadcrumb listItem={list} separator={<SlashSeperator />} />
        </div>
        <div className="col-span-2 flex justify-end md:hidden">
          <IconFilterOutline
            width={24}
            height={24}
            onClick={() => setShowFilterDrawer(true)}
          />
          {renderBadgeCount()}
        </div>
      </CenterWrapper>
    </div>
  )
}

export default SearchBreadCrumb
