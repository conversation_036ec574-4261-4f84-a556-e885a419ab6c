"use client"

import { But<PERSON> } from "@kickavenue/ui/components/Button"
import {
  IconWishlistBold,
  IconWishlistOutline,
} from "@kickavenue/ui/components/icons"

import { ModalConstant } from "@constants/modal"
import WishlistSaveModal from "@components/Wishlist/WishlistSaveModal"
import { useProductStore } from "stores/productStore"

import useWishlistButton from "./hook/useWishlisthButton"

const { WISHLIST_SAVE_PRODUCT_DETAIL } = ModalConstant.MODAL_IDS

const ProductWishlistButton = () => {
  const { detail: product } = useProductStore()
  const { isInWishlist, isLoading, handleClick, onSaved } = useWishlistButton()

  const renderIcon = isInWishlist ? IconWishlistBold : IconWishlistOutline

  const renderText = isInWishlist ? "Saved to Wishlist" : "Save to Wishlist"

  return (
    <>
      <Button
        className="mb-lg !w-full"
        onClick={handleClick}
        size="lg"
        variant="secondary"
        type="button"
        disabled={isLoading}
        IconLeft={renderIcon}
      >
        {renderText}
      </Button>

      <WishlistSaveModal
        modalId={WISHLIST_SAVE_PRODUCT_DETAIL}
        product={product}
        onWishlistAdded={onSaved}
      />
    </>
  )
}

export default ProductWishlistButton
