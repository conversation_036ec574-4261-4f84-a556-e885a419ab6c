import { MiscConstant } from "@constants/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { getStripAmount } from "@utils/misc"
import { TAddon } from "types/addonn.type"
import { TBalance } from "types/balance.type"
import { FeeRate, ItemFeeRate, TFee } from "types/fee.type"
import { EShippingType } from "types/itemListing.type"
import { TListingItem } from "types/listingItem.type"
import { EPaymentMethodType, TPaymentMethod } from "types/paymentMethod.type"
import { SellerListing } from "types/sellerListing"
import {
  EVoucherDeductTarget,
  EVoucherDiscountType,
  EVoucherType,
  TVoucher,
} from "types/voucher.type"

const { VirtualAccount } = EPaymentMethodType
const { Fixed, Percentage } = EVoucherDiscountType
const {
  ItemPrice,
  ShippingFee,
  ProcessingFee: DeductProcessingFee,
} = EVoucherDeductTarget
const { Discount, Cashback } = EVoucherType

const {
  SUMMARY_VOUCHER_TITLE,
  SUMMARY_SELLER_CREDIT_TITLE,
  SUMMARY_KICK_POINTS_TITLE,
  SUMMARY_KICK_CREDIT_TITLE,
  SUMMARY_CREDIT_USAGE_TITLE,
} = MiscConstant

const { PAYMENT_STATUS } = PageRouteConstant

export interface PaymentSummaryItem {
  title: string
  value: number
  type?: "addition" | "deduction"
  subitems?: PaymentSummaryItem[]
  tooltip?: string
}

export const calculateServiceFee = (productPrice: number, feeData: TFee) => {
  let fee = 0

  switch (feeData.type) {
    case "PERCENTAGE":
      fee = (getStripAmount(feeData.amount) / 100) * productPrice
      break
    case "AMOUNT":
      fee = getStripAmount(feeData.amount)
      break
    default:
      fee = 0
  }

  if (fee >= feeData.maxAmount) {
    fee = feeData.maxAmount
  }

  switch (feeData.action) {
    case "DEDUCT":
      return -fee
    case "ADD":
      return fee
    default:
      return fee
  }
}

export function generateListingData({
  shippingText,
}: {
  shippingText?: EShippingType | null
}) {
  const { Express, PreOrder } = EShippingType
  const listing: SellerListing = {
    countryId: undefined,
    isNewNoDefect: false,
    isOffline: false,
    itemCondition: "",
    itemId: null,
    quantity: 0,
    sellingPrice: null,
    sizeId: null,
    status: "",
    isHypequarterDisplay: false,
    defectDetail: [],
    note: null,
    packagingCondition: "PERFECT_BOX",
    isPreOrder: false,
    isConsignment: false,

    // temporary fix because missing 'n' from API response
    isConsigment: false,
  }

  if (shippingText === PreOrder) {
    listing.isPreOrder = true
  } else if (shippingText === Express) {
    listing.isConsignment = true

    // temporary fix because missing 'n' from API response
    listing.isConsigment = true
  }

  return listing
}

export function getVAPaymentMethod(methods: TPaymentMethod[]) {
  return methods.filter((method) => method.type === VirtualAccount)
}

export function calculateAddOnTotalPrice(addon: TAddon[]) {
  return addon?.reduce(
    (acc, item) => acc + getStripAmount(item.sellingPrice),
    0,
  )
}

export function calculateAddonSummary(addon: TAddon[]) {
  const values = addon
    ?.filter((item) => item.selectedQuantity && item.selectedQuantity > 0)
    ?.map((item) => ({
      title: item.item.name,
      value: getStripAmount(item.sellingPrice) * (item.selectedQuantity ?? 1),
    }))

  const totalValue = values
    ?.filter((item) => item.value !== null)
    ?.reduce((acc, item) => acc + item.value, 0)

  const subitems = values
    ?.filter((item) => item !== null)
    ?.map((item) => ({
      title: item.title,
      value: item.value,
      type: "addition" as PaymentSummaryItem["type"],
    }))

  return { totalValue, subitems }
}

export function prepareFeeRateBody({
  postalCode,
  listing,
  value = 0,
  weight = 0,
  description = "",
  name = "",
  sku = "",
}: {
  listing?: TListingItem | null
  description?: string
  name?: string
  sku?: string
  value?: number
  weight?: number
  postalCode: number
}): FeeRate {
  if (!listing) {
    return {
      items: [
        {
          name,
          sku,
          value,
          weight,
          description,
          height: 0,
          length: 0,
          width: 0,
          quantity: 1,
        },
      ],
      postalCode,
    }
  }

  return {
    items: [
      {
        name,
        sku,
        value,
        weight,
        description,
        height: 0,
        length: 0,
        width: 0,
        quantity: 1,
      },
    ],
    postalCode,
  }
}

export function getFeeRateItems({
  listings,
}: {
  listings?: TListingItem[] | TAddon[] | null
}) {
  return listings?.map((listing) => ({
    name: listing.item.name ?? "",
    sku: listing.item.sku ?? "",
    value: getStripAmount(listing.sellingPrice) ?? 0,
    weight: Math.round(listing.item.weight ?? 0),
    description: listing.item.description ?? "",
    height: 1,
    length: 1,
    width: 1,
    quantity: 1,
  })) as ItemFeeRate[]
}

export function getVoucherDeductValue({
  voucher,
  sellingPrice,
  shippingFee,
  processingFee,
  totalAddOnPrice,
}: {
  voucher?: TVoucher
  sellingPrice: number
  shippingFee: number
  processingFee: number
  totalAddOnPrice: number
}): number {
  if (!voucher) return 0

  if (voucher.deductTarget === ItemPrice) {
    return sellingPrice
  }

  if (voucher.deductTarget === ShippingFee) {
    return shippingFee
  }

  if (voucher.deductTarget === DeductProcessingFee) {
    return processingFee
  }

  return totalAddOnPrice
}

export function getVoucherAmount({
  voucher,
  sellingPrice,
  shippingFee,
  processingFee,
  totalAddOnPrice,
}: {
  voucher?: TVoucher
  sellingPrice: number
  shippingFee: number
  processingFee: number
  totalAddOnPrice: number
}): number {
  if (!voucher) return 0

  const deductValue = getVoucherDeductValue({
    voucher,
    sellingPrice,
    shippingFee,
    processingFee,
    totalAddOnPrice,
  })

  const originalAmount = getStripAmount(voucher.amount)
  if (voucher.discountType === Fixed) {
    return originalAmount
  }

  if (voucher.discountType === Percentage) {
    return (originalAmount / 100) * deductValue
  }

  return 0
}

export function getVoucherSummary({
  voucher,
  sellingPrice,
  shippingFee,
  processingFee,
  totalAddOnPrice,
}: {
  voucher?: TVoucher | null
  sellingPrice: number
  shippingFee: number
  processingFee: number
  totalAddOnPrice: number
}): PaymentSummaryItem {
  const result: PaymentSummaryItem = {
    title: SUMMARY_VOUCHER_TITLE,
    type: "deduction",
    value: 0,
  }

  if (!voucher) return result

  let voucherAmount = getVoucherAmount({
    voucher,
    sellingPrice,
    shippingFee,
    processingFee,
    totalAddOnPrice,
  })

  let voucherType = ""
  if (voucher.voucherType === Discount) {
    voucherType = "deduction"
    voucherAmount = -voucherAmount
  } else if (voucher.voucherType === Cashback) {
    voucherType = "addition"
  }

  const maxDiscountAmount = getStripAmount(voucher.maxDiscountAmount)
  voucherAmount =
    voucherAmount > maxDiscountAmount ? maxDiscountAmount : voucherAmount

  return {
    title: SUMMARY_VOUCHER_TITLE,
    type: voucherType as PaymentSummaryItem["type"],
    value: voucherAmount,
  }
}

export function calculateTotalPayment(items: PaymentSummaryItem[]) {
  return items
    ?.filter((item) => {
      if (item.title === SUMMARY_VOUCHER_TITLE && item.type === "addition") {
        return false
      }
      return item.value !== 0
    })
    ?.reduce((acc, item) => acc + item.value, 0)
}

export function calculateBillPayment(items: PaymentSummaryItem[]) {
  return items
    ?.filter((item) => {
      if (item.title === SUMMARY_VOUCHER_TITLE && item.type === "addition") {
        return false
      }
      if (
        [
          SUMMARY_SELLER_CREDIT_TITLE,
          SUMMARY_KICK_POINTS_TITLE,
          SUMMARY_KICK_CREDIT_TITLE,
          SUMMARY_CREDIT_USAGE_TITLE,
        ].includes(item.title)
      ) {
        return false
      }
      return item.value !== 0
    })
    ?.reduce((acc, item) => acc + item.value, 0)
}

export function getWalletUsageSummary({
  userBalance,
  isCreditBalance,
  isKickPoints,
  totalPayment,
}: {
  userBalance?: TBalance | null
  isCreditBalance: boolean
  isKickPoints: boolean
  totalPayment: number
}): PaymentSummaryItem[] {
  const title = SUMMARY_CREDIT_USAGE_TITLE

  if (!userBalance || (!isCreditBalance && !isKickPoints)) {
    return [
      {
        title,
        value: 0,
        subitems: [],
      },
    ]
  }

  const kickCredit = getStripAmount(userBalance.kickCredit)
  const sellerCredit = getStripAmount(userBalance.sellerCredit)
  const kickPoints = getStripAmount(userBalance.kickPoint)

  let remainingPayment = totalPayment

  const sellerCreditUsage = isCreditBalance
    ? Math.min(sellerCredit, remainingPayment)
    : 0
  remainingPayment -= sellerCreditUsage

  const kickCreditUsage = isCreditBalance
    ? Math.min(kickCredit, remainingPayment)
    : 0
  remainingPayment -= kickCreditUsage

  const kickPointsUsage = isKickPoints
    ? Math.min(kickPoints, remainingPayment)
    : 0

  return [
    {
      title,
      value: -(kickCreditUsage + sellerCreditUsage),
      type: "deduction",
      subitems: [
        {
          title: SUMMARY_SELLER_CREDIT_TITLE,
          type: "deduction",
          value: -sellerCreditUsage,
        },
        {
          title: SUMMARY_KICK_CREDIT_TITLE,
          type: "deduction",
          value: -kickCreditUsage,
        },
      ],
    },
    ...(kickPointsUsage > 0
      ? [
          {
            title: SUMMARY_KICK_POINTS_TITLE,
            type: "deduction",
            value: -kickPointsUsage,
            subitems: [],
          },
        ]
      : []),
  ] as PaymentSummaryItem[]
}

export function getPaymentStatusPageLoadingKey(
  isLoading: Record<string, boolean>,
) {
  for (const key in isLoading) {
    if (key.includes(PAYMENT_STATUS)) {
      return key
    }
  }
}

export function generateListing(source: Partial<TListingItem>) {
  return {
    id: source.id,
  } as unknown as TListingItem
}

export function buildVoucherFilters({
  listing,
  addon,
}: {
  listing?: TListingItem | null
  addon?: TAddon[] | null
}) {
  const eligibleItem: number[] = []
  if (listing?.item?.id) {
    eligibleItem.push(listing.item.id)
  }

  addon?.forEach((item) => {
    if (item.item.id) {
      eligibleItem.push(item.item.id)
    }
  })

  return {
    eligibleItem,
  }
}
