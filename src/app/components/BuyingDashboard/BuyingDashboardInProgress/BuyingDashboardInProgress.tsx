import { useSearchParams } from "next/navigation"
import { useEffect, useMemo, useState } from "react"

import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import FilterDashboard from "@components/FilterDashboard"
import { formatTxDetailStatus } from "@utils/txDetail.utils"
import usePopulateFilterData from "@app/hooks/usePopulateFilterData"

import { EBuyingDashboardTab } from "../buyingDashboard.type"
import { BuyingDashboardConstant } from "../buyingDashboard.contant"

import BuyingDashboardInProgressTable from "./BuyingDashboardInProgressTable"
import { getBuyingInProgressDefaultStatus } from "./buyingDashboardInProgress.utils"
import useBuyingInProgressSearchParams from "./hooks/useBuyingInProgressSearchParams"
import BuyingDashboardInProgressDetailModal from "./BuyingDashboardInProgressDetailModal"
import BuyingDashboardInProgressReceiveConfirm from "./BuyingDashboardInProgressReceiveConfirm"

const { Offers, BuyingInProgress } = EBuyingDashboardTab
const { TAB, PAGE, PAGE_SIZE, STATUS, SORT_BY } =
  BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardInProgress = () => {
  const searchParams = useSearchParams()
  const { handleBulkChangeQuery } = useURLQuery()
  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams.get(TAB) || Offers

  const status = useMemo(
    () => formatTxDetailStatus(getBuyingInProgressDefaultStatus()),
    [],
  )

  useBuyingInProgressSearchParams()
  usePopulateFilterData({ status: status as string[] })

  useEffect(() => {
    if (
      searchParams.get(PAGE) &&
      searchParams.get(PAGE_SIZE) &&
      searchParams.get(STATUS) &&
      searchParams.get(SORT_BY)
    )
      return
    handleBulkChangeQuery({
      [PAGE]: MiscConstant.PAGING_DEFAULT.PAGE,
      [PAGE_SIZE]: MiscConstant.PAGING_DEFAULT.PAGE_SIZE,
      [STATUS]: (status as string[])?.join(","),
      [SORT_BY]: `created_at,DESC`,
    })
  }, [handleBulkChangeQuery, searchParams, status])

  const renderFilterDashboard =
    openFilter && selectedTab === BuyingInProgress ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <BuyingDashboardInProgressTable />
      <BuyingDashboardInProgressDetailModal />
      <BuyingDashboardInProgressReceiveConfirm />
      {renderFilterDashboard}
    </>
  )
}

export default BuyingDashboardInProgress
