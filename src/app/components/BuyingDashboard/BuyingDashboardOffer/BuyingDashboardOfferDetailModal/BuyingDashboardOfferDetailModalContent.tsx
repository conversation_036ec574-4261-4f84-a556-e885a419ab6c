import Text from "@kickavenue/ui/components/Text"
import { Badge } from "@kickavenue/ui/components/Badge"
import Divider from "@kickavenue/ui/components/Divider"
import moment from "moment"
import { FormProvider } from "react-hook-form"
import { Button } from "@kickavenue/ui/components/Button"
import { useMemo } from "react"

import useGetOfferById from "@app/hooks/useGetOfferById"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { getOfferStatusMap } from "@components/BuyingDashboard/buyingDashboard.utils"
import { EOfferType, EStatusOffer } from "types/offer.type"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { adaptOfferToListingItem } from "@utils/listingItemAdapter"
import { getOfferCondition } from "@utils/offer.utils"
import ModalRoundedContent from "@components/shared/ModalParts/ModalRoundedContent"
import ModalRoundedContentItem from "@components/shared/ModalParts/ModalRoundedContentItem"
import { MiscConstant } from "@constants/misc"
import useBuyingDashboardSubmitUpdatePrice from "@components/BuyingDashboard/hooks/useBuyingDashboardSubmitUpdatePrice"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"

import BuyingDashboardOfferDetailModalStatistic from "./BuyingDashboardOfferDetailModalStatistic"
import BuyingDashboardOfferDetailModalForm from "./BuyingDashboardOfferDetailModalForm"

const { DATE_FORMAT } = MiscConstant

const BuyingDashboardOfferDetailModalContent = () => {
  const { selectedRowKeys } = useBuyingOfferStore()
  const offerId = selectedRowKeys?.[0]

  const { data: offer, isLoading } = useGetOfferById({
    id: offerId,
  })

  const { form, onFormValid, disabled } =
    useBuyingDashboardSubmitUpdatePrice(offer)

  const offerStatusMap = getOfferStatusMap(offer?.status as EStatusOffer)

  const offerType = useMemo(() => {
    if (offer?.isConsigment || offer?.isConsignment) {
      return EOfferType.Express
    }
    if (offer?.isPreOrder) {
      return EOfferType.PreOrder
    }
    return EOfferType.Standard
  }, [offer])

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onFormValid)}>
        <div className="flex flex-col gap-lg p-lg">
          <div className="flex justify-between">
            <Text size="base" type="bold" state="primary">
              Offer Status
            </Text>
            <Badge text={offerStatusMap.text} type={offerStatusMap.badgeType} />
          </div>
          <Divider orientation="horizontal" />
          <ProductDetailPreview
            listing={adaptOfferToListingItem(offer)}
            listingCondition={getOfferCondition(offer)}
          />
          <Divider orientation="horizontal" />
          <BuyingDashboardOfferDetailModalStatistic
            sizeId={offer?.size?.id as number}
            summary={offer?.item?.itemSummary}
            offerType={offerType}
          />
          <BuyingDashboardOfferDetailModalForm
            itemSummary={offer?.item?.itemSummary}
            sizeId={offer?.size?.id as number}
          />
          <ModalRoundedContent title="Offer Detail">
            <ModalRoundedContentItem
              text="Offer Date"
              value={moment(offer?.createdAt).format(DATE_FORMAT)}
            />
            <ModalRoundedContentItem
              text="Expiry Date"
              value={moment(offer?.expiredAt).format(DATE_FORMAT)}
            />
          </ModalRoundedContent>
        </div>
        <ModalFooter>
          <div className="col-span-12">
            <Button
              size="lg"
              variant="primary"
              disabled={disabled || isLoading}
              className="!w-full"
              type="submit"
            >
              Submit
            </Button>
          </div>
        </ModalFooter>
      </form>
    </FormProvider>
  )
}

export default BuyingDashboardOfferDetailModalContent
