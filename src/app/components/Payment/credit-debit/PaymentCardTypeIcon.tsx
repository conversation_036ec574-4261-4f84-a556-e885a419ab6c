import {
  IconAmericanExpress,
  IconGPN,
  IconJCB,
  IconMasterCard,
  IconPaymentBulk,
  IconVisa,
} from "@kickavenue/ui/components/icons"

import { ECardNumberType } from "types/midtrans.type"

const { Mastercard, Visa, Jcb, Amex, Gpn } = ECardNumberType

const PaymentCardTypeIcon = ({ binBrand }: { binBrand?: string }) => {
  const cardType = binBrand?.toLowerCase()

  if (cardType === Mastercard) return <IconMasterCard className="!h-6 !w-6" />
  if (cardType === Visa) return <IconVisa className="!h-6 !w-6" />
  if (cardType === Jcb) return <IconJCB className="!h-6 !w-6" />
  if (cardType === Amex) return <IconAmericanExpress className="!h-6 !w-6" />
  if (cardType === Gpn) return <IconGPN className="!h-6 !w-6" />

  return <IconPaymentBulk className="!h-6 !w-6 text-green" />
}

export default PaymentCardTypeIcon
