import Statistic from "@kickavenue/ui/components/Statistic"

export interface TPriceStatisticGridProps {
  lowestAsk?: string
  highestOffer?: string
}

const PriceStatisticGrid = ({
  lowestAsk,
  highestOffer,
}: TPriceStatisticGridProps) => {
  return (
    <div className="grid grid-cols-4 rounded-sm bg-gray-w-95 p-sm">
      <div className="col-span-2 flex justify-center border-r border-gray-w-80">
        <Statistic
          type="basic"
          title="Lowest Ask"
          content={lowestAsk || "-"}
          className="[&>div>div:nth-child(2)]:!text-gray-b-65"
        />
      </div>
      <div className="col-span-2 flex justify-center">
        <Statistic
          type="basic"
          title="Highest Offer"
          content={highestOffer || "-"}
          className="[&>div>div:nth-child(2)]:!text-gray-b-65"
        />
      </div>
    </div>
  )
}

export default PriceStatisticGrid
