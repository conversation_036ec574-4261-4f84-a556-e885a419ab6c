"use client"

import {
  IconKickCreditBulkColor,
  IconKickPointsBulkColor,
  IconSellerCreditBulkColor,
} from "@kickavenue/ui/components/icons"

import WalletPoint from "@app/components/Profile/WalletPoint"
import { formatStripePrice } from "@utils/misc"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"

const Wallet = () => {
  const { balanceData } = useGetCurrentBalanceAndFee()

  return (
    <div className="flex flex-col space-y-xs rounded-base bg-white p-xs">
      <WalletPoint
        title="Kick Credit"
        icon={<IconKickCreditBulkColor className="size-base" />}
        value={formatStripePrice(balanceData?.kickCredit)}
      />

      <WalletPoint
        title="Kick Point"
        icon={<IconKickPointsBulkColor className="size-base" />}
        value={formatStripePrice(balanceData?.kickPoint, "KP")}
      />

      <WalletPoint
        title="Seller Credit"
        icon={<IconSellerCreditBulkColor className="size-base" />}
        value={formatStripePrice(balanceData?.sellerCredit)}
      />
    </div>
  )
}

export default Wallet
