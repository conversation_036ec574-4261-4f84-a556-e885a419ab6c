import Text from "@kickavenue/ui/components/Text"
import Divider from "@kickavenue/ui/components/Divider"
import Tooltip from "@kickavenue/ui/components/Tooltip"

export interface ProductDetailItemProps {
  title: string
  value: string
  showDivider?: boolean
}

const MAX_VALUE_LENGTH = 17

const ProductDetailItem = ({
  title,
  value,
  showDivider = true,
}: ProductDetailItemProps) => {
  const renderDevider = showDivider ? (
    <div className="h-[38px]">
      <Divider type="solid" orientation="vertical" />
    </div>
  ) : null

  return (
    <div className="flex w-full justify-between">
      <div className="flex flex-col gap-xxs pr-sm">
        <Text size="sm" type="regular" state="primary">
          {title}
        </Text>
        {value?.length > MAX_VALUE_LENGTH && (
          <Tooltip
            direction="top"
            text={value}
            classNameContent="!max-w-[300px] !min-w-[200px] !text-wrap"
          >
            <Text
              size="sm"
              type="regular"
              state="secondary"
              className="text-nowrap"
            >
              {`${value.slice(0, MAX_VALUE_LENGTH)}...`}
            </Text>
          </Tooltip>
        )}
        {value?.length <= MAX_VALUE_LENGTH && (
          <Text
            size="sm"
            type="regular"
            state="secondary"
            className="text-nowrap"
          >
            {value}
          </Text>
        )}
      </div>
      {renderDevider}
    </div>
  )
}

export default ProductDetailItem
