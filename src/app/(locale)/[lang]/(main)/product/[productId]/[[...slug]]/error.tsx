"use client"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="my-md flex w-full justify-center">
      <div className="text-left">
        <h2>Something went wrong!</h2>
        <p className="text-sm">message: {error?.message || "-"}</p>
        <button type="button" onClick={() => reset()}>
          Try again
        </button>
      </div>
    </div>
  )
}
