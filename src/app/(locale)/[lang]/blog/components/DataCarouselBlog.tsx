"use client"

import React, { useRef, ReactNode } from "react"
import Carousel from "@kickavenue/ui/components/Carousel/Carousel"
import { TCarouselRef } from "@kickavenue/ui/components/Carousel/Carousel.type"

export interface DataItem {
  id: string | number
  [key: string]: any
}

interface DataCarouselProps<T extends DataItem> {
  data: T[]
  renderItem: (item: T) => ReactNode
  itemsPerSlide?: number
}

const DataCarousel = <T extends DataItem>({
  data,
  renderItem,
  itemsPerSlide = 4,
}: DataCarouselProps<T>) => {
  const ref = useRef<TCarouselRef>(null)

  const slidesContent = data.reduce((slides: T[][], item, index) => {
    if (index % itemsPerSlide === 0) {
      slides.push([])
    }
    slides[slides.length - 1].push(item)
    return slides
  }, [])

  const getGridColumns = (count: number): string => {
    const gridMap: Record<string, string> = {
      one: "grid-cols-1",
      two: "grid-cols-2",
      three: "grid-cols-3",
      four: "grid-cols-4",
      five: "grid-cols-5",
      six: "grid-cols-6",
    }

    const countToString = (num: number): string => {
      const countMap: Record<string, string> = {
        ONE: "one",
        TWO: "two",
        THREE: "three",
        FOUR: "four",
        FIVE: "five",
        SIX: "six",
      }
      return countMap[Object.keys(countMap)[num - 1]] || String(num)
    }

    return (
      gridMap[countToString(count) as keyof typeof gridMap] ||
      `grid-cols-${count}`
    )
  }

  return (
    <div className="!w-full">
      <Carousel className="px-12 !pt-8" ref={ref} size="lg" variant="rectangle">
        {slidesContent?.map((slide) => (
          <div
            key={`slide-${slide.map((item) => item.id).join("-")}`}
            className="px-3"
          >
            <div className={`grid ${getGridColumns(itemsPerSlide)} gap-6`}>
              {slide.map((item) => (
                <React.Fragment key={item.id}>
                  {renderItem(item)}
                </React.Fragment>
              ))}
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  )
}

export default DataCarousel
