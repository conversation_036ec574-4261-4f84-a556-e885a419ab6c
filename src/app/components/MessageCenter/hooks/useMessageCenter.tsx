import { useState } from "react"

import { tabItems, MessageCenterProps } from "../utils/messagecenter.utils"
import MessageContainer from "../components/MessageCenterContainer"

export const useMessageCenter = (props: MessageCenterProps) => {
  const { allMessages, unreadMessages } = props
  const [tab, setTab] = useState(tabItems[0])

  const renderTabComponent = () => {
    switch (tab.id) {
      case 0:
        return <MessageContainer list={allMessages} />
      case 1:
        return <MessageContainer list={unreadMessages} />
    }
  }

  if (unreadMessages.length > 0) {
    tabItems[1].name = `Unread (${unreadMessages.length})`
  }

  return {
    tabItems,
    tab,
    setTab,
    renderTabComponent,
  }
}
