import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"

import ConsignmentHistoryDetailModalContent from "./ConsignmentHistoryDetailModalContent"

const { CONSIGNMENT_HISTORY_DETAIL } = ModalConstant.MODAL_IDS

const ConsignmentHistoryDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()

  if (!open || modalId !== CONSIGNMENT_HISTORY_DETAIL) return null

  return (
    <Modal modalId={CONSIGNMENT_HISTORY_DETAIL} className="!min-h-[612px]">
      <HeaderModal
        onClose={() => setOpen(false, CONSIGNMENT_HISTORY_DETAIL)}
        title="Consignment Detail"
      />
      <ConsignmentHistoryDetailModalContent />
    </Modal>
  )
}

export default ConsignmentHistoryDetailModal
