import { Check<PERSON><PERSON>, Divider } from "@kickavenue/ui/dist/src/components"
import { useCallback, useState } from "react"
import { useSearchParams } from "next/navigation"

import useFetchCategories from "@app/hooks/useFetchCategories"
import useUrlQuery from "@app/hooks/useUrlQuery"
import { TCategory } from "types/category.type"
import {
  getNewSelectedFilters,
  isFilterSelected,
  mapCategoryToOption,
  mapFilterParamsToOption,
} from "@utils/product.utils"
import { TProductFilterKey } from "types/product.type"
import { stringifyFilterOption } from "@components/FilterDashboard/utils"
import { event } from "@lib/gtag"

import { FilterHeader } from "./FilterHeader"
import ShowMoreButton from "./ShowMoreButton"

const { CategoryId, Category } = TProductFilterKey

const FilterCategory = ({
  cachedCategories,
}: {
  cachedCategories?: TCategory[]
}) => {
  const { categories } = useFetchCategories({
    enabled: !cachedCategories?.length,
  })

  const [isOpen, setIsOpen] = useState(false)
  const [showMore, setShowMore] = useState(false)
  const { handleBulkChangeQuery } = useUrlQuery()
  const searchParams = useSearchParams()

  const currCategories = categories?.filter((c) => !c.parentId) || []

  const handleCheckboxChange = useCallback(
    (category: TCategory) => {
      const filterOptions = mapFilterParamsToOption({
        params: searchParams?.toString(),
        labelKey: Category,
        valueKey: CategoryId,
      })
      const categoryOption = mapCategoryToOption(category)
      const newSelectedCategories = getNewSelectedFilters(
        filterOptions,
        categoryOption,
      )
      handleBulkChangeQuery({
        [CategoryId]: stringifyFilterOption("value", newSelectedCategories),
        [Category]: stringifyFilterOption("label", newSelectedCategories),
      })

      event({
        action: "product_searched",
        params: {
          category: newSelectedCategories.map((c) => c.label).join(" | "),
        },
      })
    },
    [searchParams, handleBulkChangeQuery],
  )

  const isCheckboxChecked = useCallback(
    (category: TCategory) =>
      isFilterSelected(
        mapFilterParamsToOption({
          params: searchParams?.toString(),
          labelKey: Category,
          valueKey: CategoryId,
        }),
        mapCategoryToOption(category),
      ),
    [searchParams],
  )

  return (
    <div className="my-4">
      <FilterHeader
        title="Category"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
      />
      {isOpen && (
        <>
          {currCategories
            .slice(0, showMore ? undefined : 10)
            ?.map((category) => (
              <div key={category.id} className="mb-2">
                <CheckBox
                  checked={isCheckboxChecked(category)}
                  onChange={() => handleCheckboxChange(category)}
                  label={category.name as string}
                  className="!mb-3"
                />
              </div>
            ))}
          {!showMore && currCategories?.length > 10 && (
            <ShowMoreButton
              showMore={!showMore}
              onClick={() => setShowMore(true)}
            />
          )}
        </>
      )}
      <Divider orientation="horizontal" className="!mt-4" />
    </div>
  )
}

export default FilterCategory
