"use client"

import { Heading, Space } from "@kickavenue/ui/components"

import TabSwitcher from "@components/shared/TabSwitcher/TabSwitcher"
import { EKickPointDisbursementType } from "types/disbursement.type"

import KickPointBalance from "./KickPointBalance"
import KickPointLog from "./PointLog"

const KickPointContainer = () => {
  const tabs = [
    {
      id: EKickPointDisbursementType.KickPointLog,
      title: "Kick Point Log",
      content: <KickPointLog />,
    },
  ]

  return (
    <div className="size-full overflow-y-auto p-sm lg:p-lg">
      <div className="flex items-center justify-between">
        <Heading heading="4" textStyle="bold">
          Kick Point
        </Heading>
      </div>
      <Space size="lg" type="margin" direction="y" />
      <KickPointBalance />
      <Space size="lg" type="margin" direction="y" />
      <TabSwitcher
        tabs={tabs}
        defaultTabId={EKickPointDisbursementType.KickPointLog}
      />
    </div>
  )
}

export default KickPointContainer
