import { useMemo } from "react"

import { useAddressStore } from "stores/addressStore"
import { useCheckoutStore } from "stores/checkoutStore"
import { usePaymentStore } from "stores/paymentStore"
import { TTransactionBodyParams } from "types/transaction.type"
import { EPaymentMethodType } from "types/paymentMethod.type"
import { TOffer } from "types/offer.type"
import { TListingItem } from "types/listingItem.type"
import { getPaymentMethodByType } from "@utils/paymentMethod.utils"

import {
  calculateBillPayment,
  PaymentSummaryItem,
} from "../utils/checkout.utils"

const { FullWallet, EWallet } = EPaymentMethodType

const usePrepareCheckoutPayload = ({
  offer,
  listing,
  itemPrices,
}: {
  offer?: TOffer | null
  listing?: TListingItem | null
  itemPrices: PaymentSummaryItem[]
}) => {
  const { selectedAddress, addresses } = useAddressStore()
  const { voucher, addon } = useCheckoutStore()
  const {
    paymentToken,
    paymentType,
    isCreditBalance,
    isKickPoints,
    listPaymentMethod,
  } = usePaymentStore()

  const address = selectedAddress ?? addresses.find((item) => item.isPrimary)

  const payload: TTransactionBodyParams = useMemo(() => {
    const payload = {
      paymentMethodId: paymentType?.id ?? 0,
      sellerListingIds: [
        listing?.id ?? 0,
        ...(addon || []).map((item) => item.id),
      ],
      shippingAddressId: address?.id ?? 0,
      installmentType: "Full Payment",
      source: "marketplace",
      isUsingCreditBalance: isCreditBalance,
      isUsingKickPoint: isKickPoints,
      amount: Math.round(calculateBillPayment(itemPrices)),
    } as TTransactionBodyParams

    if (voucher?.code) {
      payload.voucherCode = voucher.code
    }

    if (paymentToken) {
      payload.token = paymentToken
    }

    if (isCreditBalance || isKickPoints) {
      payload.paymentMethodId =
        getPaymentMethodByType(listPaymentMethod, [FullWallet, EWallet])?.[0]
          ?.id ?? 0
    }

    if (offer?.id) {
      payload.offerId = offer.id
    }

    return payload
  }, [
    paymentType,
    listing,
    addon,
    address,
    isCreditBalance,
    isKickPoints,
    listPaymentMethod,
    paymentToken,
    voucher,
    itemPrices,
    offer?.id,
  ])

  return payload
}

export default usePrepareCheckoutPayload
