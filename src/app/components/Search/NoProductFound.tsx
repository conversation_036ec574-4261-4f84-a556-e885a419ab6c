import Empty from "@kickavenue/ui/components/Empty"
import { Button } from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"

import CenterWrapper from "@components/shared/CenterWrapper"

const NoProductFound = () => {
  const router = useRouter()
  return (
    <CenterWrapper>
      <div className="col-span-4 flex justify-center md:col-span-12">
        <Empty
          title="No Item Found"
          subText="Try different keywords or check your spelling."
          actionButton={
            <Button
              size="md"
              variant="primary"
              onClick={() => router.push("/search")}
            >
              Explore Other Products
            </Button>
          }
          className="[&>img]:size-[205px]"
        />
      </div>
    </CenterWrapper>
  )
}

export default NoProductFound
