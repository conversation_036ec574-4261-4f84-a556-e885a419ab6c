"use client"

import { Text, IconCopyBulkGreen } from "@kickavenue/ui/components"

interface InfoRowProps {
  label: string
  value: string
  showIcon?: boolean
  classNameValue?: string
  classNameLabel?: string
  onClickIcon?: (value: string) => void
  typeLabel?: string
  typeValue?: string
}

const InfoRow = ({
  label,
  value,
  showIcon = false,
  onClickIcon,
  classNameValue,
  classNameLabel,
  typeLabel = "regular",
  typeValue = "regular",
}: InfoRowProps) => {
  const getType = (type: string) => {
    switch (type) {
      case "bold":
        return "bold"
      case "underline":
        return "underline"
      case "strikethrough":
        return "strikethrough"
      case "italic":
        return "italic"
      case "medium":
        return "medium"
      default:
        return "regular"
    }
  }

  return (
    <div className="flex items-center justify-between">
      <Text
        size="sm"
        state="primary"
        type={getType(typeLabel)}
        className={classNameLabel}
      >
        {label}
      </Text>
      <div className="flex items-center gap-2">
        <Text
          size="sm"
          state="primary"
          type={getType(typeValue)}
          className={classNameValue}
        >
          {value}
        </Text>
        {showIcon && (
          <IconCopyBulkGreen
            className="cursor-pointer"
            onClick={() => onClickIcon && onClickIcon(value)}
          />
        )}
      </div>
    </div>
  )
}

export default InfoRow
