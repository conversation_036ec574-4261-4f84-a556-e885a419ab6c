import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"

import ConsignmentActiveDetailModalContent from "./ConsignmentActiveDetailModalContent"

const { CONSIGNMENT_ACTIVE_DETAIL } = ModalConstant.MODAL_IDS

const ConsignmentActiveDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()

  if (!open || modalId !== CONSIGNMENT_ACTIVE_DETAIL) return null

  return (
    <Modal modalId={CONSIGNMENT_ACTIVE_DETAIL} className="!min-h-[612px]">
      <HeaderModal
        onClose={() => setOpen(false, CONSIGNMENT_ACTIVE_DETAIL)}
        title="Listing Detail"
      />
      <ConsignmentActiveDetailModalContent />
    </Modal>
  )
}

export default ConsignmentActiveDetailModal
