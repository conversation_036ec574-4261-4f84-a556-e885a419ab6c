"use client"

import React from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  IconCloseOutline,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"

import Modal from "@app/components/shared/Modal"
import { TPaginatedData } from "types/apiResponse.type"
import { PreferenceType, TPreferences } from "types/preference.type"
import SearchInput from "@components/shared/SearchInput"
import { ModalConstant } from "@constants/modal"
import Spinner from "@components/shared/Spinner/Spinner"
import { useModalStore } from "stores/modalStore"

import useBrandPreference from "./hooks/useBrandPreference"

const BrandEdit = ({
  preference,
}: {
  preference: TPaginatedData<TPreferences>
}) => {
  const router = useRouter()
  const { setOpen, modalId, modalData } = useModalStore()
  const preferenceId = preference?.content.find(
    (item) => item.type === PreferenceType.Brand,
  )?.id

  const {
    saveMutation,
    data,
    selectedBrands,
    search,
    isLoading,
    handleSearch,
    handleCheckboxChange,
  } = useBrandPreference({
    initialBrands: modalData,
    preferenceId,
    onSuccessSave: () => {
      setOpen(false, modalId, null)
      router.refresh()
    },
  })

  const { PREFERENCES_BRAND_EDIT } = ModalConstant.MODAL_IDS
  if (modalId !== PREFERENCES_BRAND_EDIT) return

  return (
    <>
      <Modal modalId={PREFERENCES_BRAND_EDIT}>
        <div className="flex justify-between p-md">
          <div className="flex grow justify-center text-right">
            <Heading heading="5" textStyle="bold">
              Brand
            </Heading>
          </div>
          <IconCloseOutline
            onClick={() => setOpen(false)}
            className="scale-150 cursor-pointer"
          />
        </div>
        <Divider orientation="horizontal" />
        <div className="p-lg pb-sm">
          <SearchInput
            value={search}
            onChange={(e) => handleSearch(e.currentTarget.value)}
            onClearText={() => handleSearch("")}
          />
        </div>
        <div className="h-[496px] overflow-y-scroll">
          {isLoading && (
            <div className="flex h-full items-center justify-center">
              <Spinner />
            </div>
          )}
          {!isLoading && (
            <div className="flex flex-col gap-y-base p-lg">
              {data?.map((item) => (
                <CheckBox
                  onChange={() => handleCheckboxChange(item)}
                  key={item.id}
                  checked={selectedBrands.some((brand) => brand.id === item.id)}
                  label={item.name}
                />
              ))}
            </div>
          )}
        </div>
        <div className="p-lg shadow-base">
          <Button
            onClick={() => {
              saveMutation.mutate()
            }}
            size="lg"
            variant="primary"
            className="!w-full"
            disabled={selectedBrands.length === 0 || saveMutation.isPending}
          >
            Save
          </Button>
        </div>
      </Modal>
    </>
  )
}

export default BrandEdit
