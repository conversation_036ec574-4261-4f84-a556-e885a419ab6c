import { useEffect } from "react"
import { useSearchParams } from "next/navigation"

import {
  ConsignmentDashboardTabEnum,
  TListingStatusEnum,
} from "types/listingItem.type"
import { useConsignmentActiveStore } from "stores/consignmentActiveStore"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { joinArrayToString } from "@utils/misc"
import { MiscConstant } from "@constants/misc"
import { getHasOfferFilter, getDateFilter } from "@utils/selling"
import { getConditionFilter } from "@utils/listingItem"
import { getFilterFromQuery } from "@utils/query.utils"
import { stringToNumberArray, stringToStringArray } from "@utils/string.utils"

const {
  PAGE,
  PAGE_SIZE,
  TAB,
  STATUS,
  CONDITION,
  SIZE_ID,
  CATEGORIES,
  SEARCH,
  HAS_OFFER,
  LISTING_PRICE_MIN,
  LISTING_PRICE_MAX,
  CREATED_DATE_START,
  CREATED_DATE_END,
} = ListingItemConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const useConsignmentActiveSearchParams = () => {
  const searchParams = useSearchParams()
  const { setFilter } = useConsignmentActiveStore()

  useEffect(() => {
    const tab = searchParams.get(TAB)
    if (
      tab !== ConsignmentDashboardTabEnum.Active ||
      !searchParams.toString()
    ) {
      return
    }
    const status =
      searchParams.get(STATUS) ||
      joinArrayToString([TListingStatusEnum.ListingApproved])

    const page = searchParams.get(PAGE)
    const pageSize = searchParams.get(PAGE_SIZE)
    const condition = searchParams.get(CONDITION)
    const sizeId = searchParams.get(SIZE_ID)
    const categories = searchParams.get(CATEGORIES)
    const search = searchParams.get(SEARCH)
    const hasOffer = searchParams.get(HAS_OFFER)
    const listingPriceMin = searchParams.get(LISTING_PRICE_MIN)
    const listingPriceMax = searchParams.get(LISTING_PRICE_MAX)
    const createdDateStart = searchParams.get(CREATED_DATE_START)
    const createdDateEnd = searchParams.get(CREATED_DATE_END)

    const filter = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      totalPages: 0,
      sortBy: undefined,
      status: stringToStringArray(status),
      consignmentToggle: true,
      hasQuantity: true,
      sizeId: stringToNumberArray(sizeId),
      categoryName: stringToStringArray(categories),
      search,
      hasOffer: getHasOfferFilter(hasOffer),
      priceFrom: getFilterFromQuery(listingPriceMin) as number,
      priceTo: getFilterFromQuery(listingPriceMax) as number,
      sellDateFrom: getDateFilter(createdDateStart),
      sellDateTo: getDateFilter(createdDateEnd),
      ...getConditionFilter(condition),
    }
    setFilter(filter)
  }, [searchParams, setFilter])
}

export default useConsignmentActiveSearchParams
