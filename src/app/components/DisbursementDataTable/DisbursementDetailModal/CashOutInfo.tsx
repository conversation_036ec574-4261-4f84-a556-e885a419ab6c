"use client"

import { Text } from "@kickavenue/ui/components"

import useGetMyDisbursementById from "@app/hooks/useGetMyDisbursementById"

const CashOutTo = () => {
  const { disbursementData } = useGetMyDisbursementById()

  const bankInfo = disbursementData?.metadata?.bankInfo
  if (!bankInfo) return null

  return (
    <div className="flex flex-col gap-base">
      <Text size="base" state="primary" type="bold">
        Cash Out To
      </Text>

      <div className="flex flex-col gap-2 rounded-sm border border-gray-w-80 p-4">
        <div className="flex flex-col gap-2">
          <Text size="sm" state="primary" type="bold">
            {bankInfo.name}
          </Text>
          <div className="flex items-center gap-2">
            <Text size="sm" state="secondary" type="regular">
              {bankInfo.bank}
            </Text>
            <Text size="sm" state="secondary" type="regular">
              {bankInfo.account}
            </Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CashOutTo
