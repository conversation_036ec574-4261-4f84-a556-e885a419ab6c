import Link from "next/link"
import { IconArrowUp } from "@kickavenue/ui/components/icons"

import { FooterTextLink } from "./footer.utils"

interface FooterTextLinkProps extends FooterTextLink {}

const FooterMenuLink = ({ text, url, ...rest }: FooterTextLinkProps) => {
  const renderArrowUpIcon = rest?.arrowUpIcon ? <IconArrowUp /> : null
  return (
    <Link href={url} className="leading-none">
      <div className="mb-sm flex items-center gap-xxs">
        <p className="m-0 text-sm">{text}</p>
        {renderArrowUpIcon}
      </div>
    </Link>
  )
}

export default FooterMenuLink
