"use client"

import { Carousel } from "@kickavenue/ui/components/Carousel/Carousel"
import { snakeCase } from "lodash"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"

import { event } from "@lib/gtag"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useMemberStore } from "stores/memberStore"
import { IMenuWizardSection } from "types/menuWizard"

interface BannerProps {
  data: IMenuWizardSection
}

const getBannerRedirectionUrl = (data: IMenuWizardSection): string => {
  let redirectionUrl = "#"
  if (!data.sectionContent?.redirectionRule) return redirectionUrl

  const redirection = data.sectionContent.redirectionRule

  let url = ""
  switch (redirection.type) {
    case "COLLECTION":
      redirectionUrl = `/collection/${redirection.keywords}`
      break
    case "ITEM":
      // Redirect to search page with item filter
      redirectionUrl = `/search?keyword=${encodeURIComponent(redirection.keywords)}`
      break
    case "RAFFLE":
      redirectionUrl = `/raffle/${redirection.keywords}`
      break
    case "BRAND":
      // Redirect to search page with brand filter
      redirectionUrl = `/search?brandId=${encodeURIComponent(redirection.keywords)}`
      break
    case "SEARCH":
      redirectionUrl = `/search?keyword=${encodeURIComponent(redirection.keywords)}`
      break
    case "CUSTOM":
      url = redirection.keywords
      redirectionUrl =
        !url.startsWith("http://") && !url.startsWith("https://")
          ? `https://${url}`
          : url
      break
    default:
      redirectionUrl = "#"
      break
  }
  return redirectionUrl
}

const BannerHome = ({ data }: BannerProps) => {
  const [isMobile, setIsMobile] = useState(false)
  const { member } = useMemberStore()

  useEffect(() => {
    // Handler to call on window resize
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Call handler right away so state gets updated with initial window size
    handleResize()

    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  if (!data) return null

  const redirectionUrl = getBannerRedirectionUrl(data)
  const isExternalUrl =
    redirectionUrl.startsWith("http") || redirectionUrl.startsWith("//")

  // Use portrait image for mobile if available, otherwise fallback to landscape
  const imageUrl =
    isMobile && data.sectionContent?.imagePortrait
      ? convertS3UrlToCloudFront(data.sectionContent.imagePortrait)
      : convertS3UrlToCloudFront(data.sectionContent?.imageLandscape || "")

  const imageMobile = isMobile ? "aspect-square w-full" : "aspect-[16/5] w-full"

  const isSingleBanner = true
  const loopIntervalMs = isSingleBanner ? undefined : 5000

  return (
    <div
      className="relative w-full"
      style={{ backgroundColor: data.backgroundColor }}
    >
      <Link
        href={redirectionUrl}
        replace={false}
        {...(isExternalUrl && { target: "_blank", rel: "noopener noreferrer" })}
        className="block w-full"
        onClick={() => {
          event({
            action: "banner_clicked",
            params: {
              [snakeCase("user_id")]: member?.id || "",
              [snakeCase("user_platform")]: window.navigator.userAgent || "",
              [snakeCase("page_url")]: redirectionUrl,
            },
          })
        }}
      >
        <Carousel
          size="lg"
          variant="line"
          className="w-full"
          withBackground
          isLoop={!isSingleBanner}
          isInfinite={!isSingleBanner}
          loopIntervalMs={loopIntervalMs}
        >
          <div className="relative w-full">
            <div className={imageMobile}>
              <Image
                alt={data.title || `Banner ${data.id}`}
                src={imageUrl}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </Carousel>
      </Link>
    </div>
  )
}

export default BannerHome
