"use client"

import TabBar from "@components/shared/TabBar"

import { tabItems } from "./utils/myvoucher.utils"
import { useMyVoucher } from "./hooks/useMyVoucher"
import VoucherModal from "./modal/VoucherModal"

export default function MyVoucher() {
  const { handleChangeTab, renderTabComponent } = useMyVoucher()
  return (
    <>
      <VoucherModal />
      <div className="flex h-full flex-col p-sm lg:p-lg">
        <h4 className="text-heading-4 font-bold text-gray-b-75">Voucher</h4>
        <div className="my-base h-10">
          <TabBar items={tabItems} onChange={handleChangeTab} />
        </div>
        <div className="flex-1 overflow-hidden">{renderTabComponent()}</div>
      </div>
    </>
  )
}
