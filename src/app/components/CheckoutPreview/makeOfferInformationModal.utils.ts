export interface MakeOfferInformationItem {
  title: string
  body: string
}

export const makeOfferInformationHeader: MakeOfferInformationItem = {
  title: "How does Offer work?",
  body: "Make smart offers and find great deals! Your offer is instantly shared with sellers once your deposit is secured.",
}

export const makeOfferInformationBody: MakeOfferInformationItem[] = [
  {
    title: "How to use your Kick Credit",
    body: "Make sure you have at least IDR 25,000 in your Kick Credit. We’ll hold this amount as a deposit, which is fully refundable when you complete your purchase.",
  },
  {
    title: "Complete your purchase, get your deposit back",
    body: "Complete your purchase when your offer is accepted, and get your deposit back. Cancellations after acceptance forfeit the deposit.",
  },
  {
    title: "Make offers you can follow through",
    body: "Make offers you intend to honor. Multiple cancellations may lead to account suspension. Keep it real!",
  },
]
