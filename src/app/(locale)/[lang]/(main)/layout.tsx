import { notFound } from "next/navigation"

import Footer from "@app/components/Footer"
import Header from "@app/components/Header"
import ExpandedSearch from "@components/ExpandedSearch"
import { defaultLocale, locales } from "@config/i18n"

interface Props {
  children: React.ReactNode
  params: { lang: string }
}

export default function LocaleLayout({ children, params: { lang } }: Props) {
  if (!locales.includes(lang ?? defaultLocale)) notFound()

  return (
    <>
      <div className="relative md:sticky md:top-0 md:z-30">
        <Header />
        <ExpandedSearch />
      </div>
      <main>{children}</main>
      <Footer />
    </>
  )
}
