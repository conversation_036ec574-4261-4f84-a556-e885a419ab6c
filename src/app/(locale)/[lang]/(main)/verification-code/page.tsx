"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Head<PERSON> } from "@kickavenue/ui/components"
import { debounce } from "lodash"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

import OTPInput from "@app/components/OtpCode"
import { useTranslations } from "@app/i18n"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useRegistrationStore } from "@stores/registrationStore"

import { useOtpVerification } from "./hooks/useOtpVerification"

const VerificationCode = () => {
  const t = useTranslations()
  const { registrationData } = useRegistrationStore()
  const router = useRouter()
  const [emailOtp, setEmailOtp] = useState("")
  const [phoneOtp, setPhoneOtp] = useState("")
  const { isLoading, smsError, emailError, handleVerify } = useOtpVerification()

  const isSubmitDisabled = emailOtp.length !== 6 || phoneOtp.length !== 6

  useEffect(() => {
    if (
      !registrationData ||
      !registrationData.email ||
      !registrationData.phoneNumber
    ) {
      router.push(PageRouteConstant.REGISTER)
    }
  }, [router, registrationData])

  if (!registrationData.email || !registrationData.phoneNumber) {
    return null
  }

  const onVerify = debounce(async () => {
    const result = await handleVerify(emailOtp, phoneOtp)

    if (result.success) {
      router.push(PageRouteConstant.VERIFICATION_SUCCESS)
    }
  }, 300)

  return (
    <div className="flex min-h-[80vh] items-center justify-center px-4">
      <div className="flex w-full max-w-md flex-col items-center">
        <Heading heading="4" textStyle="bold" className="mb-8">
          {t("verificationOTP")}
        </Heading>

        <div className="mb-6 w-full">
          <div className="mb-4 text-center text-sm">
            OTP code sent via Email to{" "}
            <span className="font-bold">{registrationData.email}</span>
          </div>
          <OTPInput onChange={setEmailOtp} type="EMAIL" error={emailError} />
        </div>
        <Divider orientation="horizontal" className="w-full" />
        <div className="my-6 w-full">
          <div className="mb-4 text-center text-sm">
            OTP code sent via WhatsApp or SMS to{" "}
            <span className="font-bold">{registrationData.phoneNumber}</span>
          </div>
          <OTPInput onChange={setPhoneOtp} type="SMS" error={smsError} />
        </div>

        <Button
          onClick={onVerify}
          disabled={isLoading || isSubmitDisabled}
          className="!w-full"
          size="lg"
        >
          Submit
        </Button>
      </div>
    </div>
  )
}

export default VerificationCode
