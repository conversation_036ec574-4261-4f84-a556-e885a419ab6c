import { useConsignmentPendingStore } from "stores/consignmentPendingStore"
import { MiscConstant } from "@constants/misc"
import useURLQuery from "@app/hooks/useUrlQuery"

const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT
const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS

const useConsignmentPendingPagination = () => {
  const { consignmentPendingFilter } = useConsignmentPendingStore()
  const { handleChangeQuery } = useURLQuery()
  const {
    page = PAGE_DEFAULT,
    pageSize = PAGE_SIZE_DEFAULT,
    totalPages = 0,
  } = consignmentPendingFilter || {}
  const handleNext = () => {
    const nextPage = page + 1
    handleChangeQuery(PAGE, nextPage)
  }
  const handlePrev = () => {
    const prevPage = page - 1
    handleChangeQuery(PAGE, prevPage)
  }
  const handlePage = (page: number) => {
    handleChangeQuery(PAGE, page - 1)
  }
  const handlePerPage = (size: number) => {
    handleChangeQuery(PAGE_SIZE, size)
    handleChangeQuery(PAGE, PAGE_DEFAULT)
  }
  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useConsignmentPendingPagination
