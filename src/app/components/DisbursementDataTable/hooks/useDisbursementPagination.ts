import { useSellingCurrentStore } from "stores/sellingCurrentStore"
import { MiscConstant } from "@constants/misc"
import useURLQuery from "@app/hooks/useUrlQuery"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS

const useDisbursementPagination = () => {
  const { sellingCurrentFilter } = useSellingCurrentStore()
  const { handleChangeQuery } = useURLQuery()
  const page = sellingCurrentFilter?.page || 0
  const pageSize =
    Number(new URLSearchParams(window.location.search).get("pageSize")) ||
    sellingCurrentFilter?.pageSize ||
    10
  const totalPages = sellingCurrentFilter?.totalPages || 1

  const handleNext = () => {
    const nextPage = page + 1
    handleChangeQuery(PAGE, nextPage.toString())
  }

  const handlePrev = () => {
    const prevPage = page - 1
    handleChangeQuery(PAGE, prevPage.toString())
  }

  const handlePage = (page: number) => {
    handleChangeQuery(PAGE, (page - 1).toString())
  }

  const handlePerPage = (size: number) => {
    handleChangeQuery(PAGE_SIZE, size.toString())
    // Reset to first page when changing page size
    handleChangeQuery(PAGE, "0")
  }

  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useDisbursementPagination
