/* eslint-disable max-lines-per-function */
import {
  <PERSON><PERSON>,
  <PERSON>B<PERSON>,
  Input as CommonInput,
  Divider,
  IconCvv,
  Label,
  Text,
} from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"
import { useEffect, useMemo, useState } from "react"
import { Controller } from "react-hook-form"
import { useHookFormMask } from "use-mask-input"

import { usePaymentCreditDebitForm } from "@components/Payment/hooks/usePaymentCreditDebitForm"
import PaymentInstallmentType from "@components/Payment/installment-type/PaymentInstallmentType"
import Input from "@components/shared/Form/Input"
import Spinner from "@components/shared/Spinner"
import { FormFieldConstant } from "@constants/formField"
import { getFieldVariant, getHelperText } from "@utils/form.utils"
import { usePaymentCardFormStore } from "stores/paymentCardFormStore"
import { BINData } from "types/bin.type"

import useCheckValidCardNumber from "../hooks/useCheckValidCardNumber"

import PaymentCardTypeIcon from "./PaymentCardTypeIcon"

const {
  CARD_NUMBER,
  CARD_EXPIRY_MONTH,
  CARD_EXPIRY_YEAR,
  CARD_CVV,
  IS_AGREED,
} = FormFieldConstant.PAYMENT_CREDIT_DEBIT

const MIN_CARD_NUMBER_LENGTH = 16
const MIN_CARD_NUMBER_LENGTH_MESSAGE = `Please complete the numbers`
const CARD_NUMBER_CAN_NOT_BE_USED = "Please enter a valid card number"

export default function PaymentCreditDebitForm() {
  const { submitDisabled, validateCardNumber, onFormValid, form } =
    usePaymentCreditDebitForm()
  const registerWithMask = useHookFormMask(form.register)
  const { setForm, setBinData } = usePaymentCardFormStore()

  const [warningCardNumber, setWarningCardNumber] = useState(false)

  const cardNumberValue = String(form.watch(CARD_NUMBER.KEY))
    .replaceAll(" ", "")
    .replaceAll("_", "")

  const { data, isLoading, isError } = useCheckValidCardNumber(cardNumberValue)

  const cardNumberFields = useMemo(
    () => ({
      // eslint-disable-next-line no-nested-ternary
      helperText: warningCardNumber
        ? MIN_CARD_NUMBER_LENGTH_MESSAGE
        : isError
          ? CARD_NUMBER_CAN_NOT_BE_USED
          : getHelperText(form, CARD_NUMBER.KEY),
      // eslint-disable-next-line no-nested-ternary
      variant: warningCardNumber
        ? "warning"
        : isError
          ? "danger"
          : getFieldVariant(form, CARD_NUMBER.KEY),
    }),
    [warningCardNumber, isError, form],
  )

  useEffect(() => {
    if (cardNumberValue.length === 0) {
      setWarningCardNumber(false)
      return
    }

    if (cardNumberValue.length < MIN_CARD_NUMBER_LENGTH) {
      setWarningCardNumber(true)
    } else {
      setWarningCardNumber(false)
    }
  }, [cardNumberValue])

  return (
    <form
      onSubmit={form.handleSubmit((values) => {
        setForm(values)
        setBinData(data as BINData)

        return onFormValid({ ...values, ...(data as BINData) })
      })}
    >
      <div className="flex flex-col">
        <div className="max-h-[70vh] overflow-y-scroll">
          <div className="flex flex-col gap-base px-lg pb-sm pt-lg">
            <div className="flex flex-col gap-sm">
              <Label size="sm" state="required" type="default">
                {CARD_NUMBER.NAME}
              </Label>
              <div className="relative flex gap-xs">
                <CommonInput
                  type="text"
                  placeholder="XXXX XXXX XXXX XXXX"
                  autoComplete="off"
                  {...registerWithMask(CARD_NUMBER.KEY, CARD_NUMBER.MASK, {
                    validate: (value) => validateCardNumber(value, isError),
                  })}
                  {...cardNumberFields}
                />

                <div
                  className={cx(
                    "absolute right-4 flex flex-col items-center justify-center",
                    data && "bottom-0 top-0",
                    isLoading && "top-2.5",
                  )}
                >
                  {data && <PaymentCardTypeIcon binBrand={data.brand} />}
                  {isLoading && <Spinner className="!h-6 !w-6" />}
                </div>
              </div>
            </div>

            <div className="flex gap-lg">
              <div className="">
                <Label size="sm" type="default">
                  Expiry Date
                </Label>
                <div className="flex gap-xs">
                  <div className="w-[48px]">
                    <Controller
                      name={CARD_EXPIRY_MONTH.KEY}
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          state="default"
                          placeholder="MM"
                          {...field}
                          maxLength={2}
                          value={String(field.value || "")}
                        />
                      )}
                    />
                  </div>
                  <div className="w-[48px]">
                    <Controller
                      name={CARD_EXPIRY_YEAR.KEY}
                      control={form.control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <Input
                          state="default"
                          placeholder="YY"
                          {...field}
                          maxLength={2}
                          value={String(field.value || "")}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-sm">
                <Label size="sm" type="default">
                  CVV
                </Label>
                <div className="flex items-center justify-center gap-xs">
                  <div className="w-[72px]">
                    <Controller
                      name={CARD_CVV.KEY}
                      control={form.control}
                      render={({ field }) => (
                        <CommonInput
                          placeholder="123"
                          {...field}
                          maxLength={3}
                          value={String(field.value || "")}
                        />
                      )}
                    />
                  </div>
                  <div className="">
                    <IconCvv />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-center gap-xs [&>label>div]:!text-sm [&>label>div]:!text-gray">
              <CheckBox label="Save card number and expiration date" />
            </div>

            <Divider orientation="horizontal" state="default" />

            <div className="flex flex-col gap-y-sm">
              <Text size="sm" type="bold" state="primary" className="text-left">
                Select Payment
              </Text>

              <PaymentInstallmentType setInstallmentType={() => {}} />
            </div>

            <div className="flex items-center gap-x-1 py-sm [&>label>div]:!text-sm [&>label>div]:!text-gray">
              <Controller
                name={IS_AGREED.KEY}
                control={form.control}
                rules={{ validate: (value) => Boolean(value) }}
                render={({ field }) => (
                  <CheckBox
                    onChange={field.onChange}
                    checked={Boolean(field.value)}
                    label="By clicking proceed, I agree to Kick Avenue's"
                    className="!ml-0"
                  />
                )}
              />
              <Button variant="link" className="!p-0">
                Terms and Conditions
              </Button>
              <Text size="sm" type="medium" state="primary">
                and
              </Text>
              <Button variant="link" className="!p-0">
                Privacy Policy
              </Button>
            </div>
          </div>
        </div>
        <div className="w-full p-lg shadow-base">
          <Button
            size="lg"
            variant="primary"
            type="submit"
            className="!w-full"
            disabled={submitDisabled && !isError}
          >
            Proceed
          </Button>
        </div>
      </div>
    </form>
  )
}
