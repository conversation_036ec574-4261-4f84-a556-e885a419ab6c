"use client"

import WishlistSnackbar from "@components/Wishlist/WishlistSnackbar"
import CenterWrapper from "@shared/CenterWrapper"
import { useMiscStore } from "stores/miscStore"

import HeaderBottomNav from "./HeaderBottomNav"
import HeaderTop from "./HeaderTop"

const Header = () => {
  const { showHeaderBottomNav } = useMiscStore()
  return (
    <>
      <HeaderTop />
      {showHeaderBottomNav && (
        <div className="bg-white shadow-[0px_4px_8px_-2px_#24242414] md:sticky md:top-[65px] md:z-20 md:border-t md:border-gray-w-90">
          <CenterWrapper className="mx-auto max-w-md overflow-x-auto text-base sm:flex sm:max-w-full sm:justify-center">
            <HeaderBottomNav />
          </CenterWrapper>
          <WishlistSnackbar className="top-[10px]" />
        </div>
      )}
    </>
  )
}

export default Header
