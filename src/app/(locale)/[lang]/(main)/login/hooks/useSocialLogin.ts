import { snakeCase } from "lodash"
import { signIn } from "next-auth/react"

interface SocialLoginProps {
  setError: (error: string) => void
  setIsLoading: (isLoading: boolean) => void
}

type Provider = "Google" | "Apple" | "Facebook"

export default function useSocialLogin({
  setError,
  setIsLoading,
}: SocialLoginProps) {
  const handleSocialLogin = async (provider: Provider) => {
    try {
      setIsLoading(true)
      setError("")

      if (provider === "Google") {
        // Redirect ke endpoint Google sign-in
        await signIn(
          "cognito",
          {
            redirect: false,
          },
          {
            [snakeCase("identityProvider")]: "Google",
            prompt: "select_account",
          },
        )
        return
      }

      // Handle other providers through NextAuth
      const result = await signIn(provider.toLowerCase(), { redirect: false })
      if (result?.error) {
        throw new Error(result.error)
      }
    } catch (err: any) {
      setError("Unable to initiate login. Please try again later.")
    } finally {
      setIsLoading(false)
    }
  }

  return {
    handleSocialLogin,
  }
}
