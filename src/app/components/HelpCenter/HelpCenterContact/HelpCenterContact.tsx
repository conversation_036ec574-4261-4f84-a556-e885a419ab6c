import { Divider, Heading, Text } from "@kickavenue/ui/dist/src/components"

import HelpCenterSection from "../HelpCenterSection"

import HelpCenterContactAction from "./HelpCenterContactAction"

const HelpCenterContact = () => {
  return (
    <HelpCenterSection childClassName="flex gap-lg px-base py-xl xl:px-0 flex-wrap xl:flex-nowrap">
      <div className="flex w-full flex-col gap-sm">
        <Heading heading="5" textStyle="bold">
          Customer Service Hours
        </Heading>
        <Divider orientation="horizontal" />
        <div className="gap-xss flex flex-col">
          <Text size="sm" type="bold" state="primary">
            Email
          </Text>
          <Text size="sm" type="regular" state="secondary">
            Submit 24/7
          </Text>
        </div>
        <div className="gap-xss flex flex-col">
          <Text size="sm" type="bold" state="primary">
            Whatsapp
          </Text>
          <Text size="sm" type="regular" state="secondary">
            Monday - Friday: 9:00 - 21:00 WIB
          </Text>
          <Text size="sm" type="regular" state="secondary">
            Saturday - Sunday: 10:00 - 18:00 WIB
          </Text>
        </div>
      </div>
      <div className="flex w-full flex-col gap-sm">
        <Heading heading="5" textStyle="bold">
          Need Help? Start Here
        </Heading>
        <Divider orientation="horizontal" />
        <Text size="sm" type="regular" state="secondary">
          Provide us with a few details, and we will guide you to the right
          solution. Contact options include email and Whatsapp!
        </Text>
        <HelpCenterContactAction />
      </div>
    </HelpCenterSection>
  )
}

export default HelpCenterContact
