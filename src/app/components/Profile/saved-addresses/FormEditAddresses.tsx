"use client"

import { Divider, Heading, IconCloseOutline } from "@kickavenue/ui/components"
import { useFormState } from "react-dom"
import { useQueryClient } from "@tanstack/react-query"
import { cx } from "class-variance-authority"

import Modal from "@app/components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { edit } from "@infrastructure/actions/address"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"
import { useAddressStore } from "stores/addressStore"
import ButtonSave from "@components/shared/Form/ButtonSave"
import useToast from "@app/hooks/useToast"
import Spinner from "@components/shared/Spinner/Spinner"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { splitPhoneNumber } from "@utils/misc"
import useFetchAddressById from "@app/hooks/useFetchAddressById"
import { ModalConstant } from "@constants/modal"

import FieldFormAddresses from "./FieldFormAddresses"
import { InitialValue } from "./hooks/useFormAddresses"

const { EDIT_ADDRESS, CHANGE_ADDRESS } = ModalConstant.MODAL_IDS

interface FormAddressesProps {
  countries: TCountry[]
  cities: TCity[]
  provinces: TProvince[]
}

const FormEditAddresses = (props: FormAddressesProps) => {
  const { countries, cities, provinces } = props
  const { setOpen, open, modalId, prevModal } = useModalStore()
  const queryClient = useQueryClient()
  const { selectedIdAddress } = useAddressStore()
  const { setShowToast } = useToast()

  const { data, isSuccess, isLoading } = useFetchAddressById(
    Number(selectedIdAddress),
  )

  const handleEditAddress = async (prevState: any, formData: FormData) => {
    formData.set("is_primary", data?.isPrimary ? "on" : "off")
    const resp = await edit(prevState, formData)
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_MY_ADDRESS],
    })
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_ADDRESS_BY_ID, selectedIdAddress],
    })
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_ALL_ADDRESS],
    })
    if (!("id" in resp)) {
      return resp
    }

    setShowToast(true, "Address successfully updated")

    if (prevModal?.modalId === CHANGE_ADDRESS) {
      setOpen(true, CHANGE_ADDRESS)
    } else {
      setOpen(false)
    }

    return resp
  }

  const [state, formAction] = useFormState(handleEditAddress, { errors: {} })

  const initialValue: InitialValue = {
    countryCode: splitPhoneNumber(data?.phoneNumber ?? "").countryCode,
    city: {
      value: String(data?.cityId),
      label: data?.cityName ?? "",
    },
    province: {
      value: String(data?.provinceId),
      label: data?.provinceName ?? "",
    },
    country: {
      value: String(data?.countryId),
      label: data?.countryName ?? "",
    },
  }

  if (!open || modalId !== EDIT_ADDRESS) return null

  return (
    <>
      <Modal modalId={EDIT_ADDRESS}>
        <div className="flex justify-between p-md">
          <div className="flex grow justify-center text-right">
            <Heading heading="5" textStyle="bold">
              Edit Address
            </Heading>
          </div>
          <IconCloseOutline
            onClick={() => setOpen(false)}
            className="scale-150 cursor-pointer"
          />
        </div>
        <Divider orientation="horizontal" />
        <form action={formAction}>
          <div
            className={cx(
              "max-h-[496px] overflow-scroll",
              isLoading && "overflow-x-hidden overflow-y-hidden",
            )}
          >
            {isLoading && (
              <div className="flex h-[496px] w-full items-center justify-center">
                <Spinner />
              </div>
            )}
            {isSuccess && (
              <FieldFormAddresses
                state={state}
                countries={countries}
                cities={cities}
                provinces={provinces}
                initialValue={initialValue}
                address={data}
              />
            )}
          </div>
          <div className="p-lg shadow-base">
            <ButtonSave />
          </div>
        </form>
      </Modal>
    </>
  )
}
export default FormEditAddresses
