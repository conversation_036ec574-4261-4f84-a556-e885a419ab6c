import { Text, Divider } from "@kickavenue/ui/components"

const DeliveryDetails = () => {
  return (
    <>
      <Divider orientation="horizontal" />
      <div className="pt-6">
        <Text className="pb-3" size="sm" type="bold" state="primary">
          Delivery Detail
        </Text>
        <div className="flex flex-col gap-y-4 text-sm text-gray-b-65">
          <div className="flex">
            <div className="w-32 text-gray">Recipient Name</div>
            <div className="w-4">:</div>
            <div><PERSON></div>
          </div>
          <div className="flex">
            <div className="w-32 text-gray">Phone</div>
            <div className="w-4">:</div>
            <div>+62 123456789</div>
          </div>
          <div className="flex">
            <div className="w-40 text-gray">Address</div>
            <div className="w-4">:</div>
            <div className="w-80">
              Jalan Tebet Barat 4 Blok B2 No. 8 (Kantor pos indonesia), Jakarta,
              Daerah Khusus Ibukota Jakarta, Indonesia, 12810
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default DeliveryDetails
