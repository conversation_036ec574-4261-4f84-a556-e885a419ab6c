import Image from "next/image"
import { Text } from "@kickavenue/ui/components"
import Link from "next/link"

interface CardBlogProps {
  title: string
  slug: string
  date: string
  image: string
  bannerImage?: boolean
}

const CardBlog = ({
  title,
  date,
  slug,
  image,
  bannerImage = true,
}: CardBlogProps) => {
  return (
    <div className="card bg-base-100 shadow-xl w-full">
      <Link href={slug} className="flex flex-col gap-3">
        {bannerImage && (
          <div className="w-max-[296px] h-max-[196px]">
            <Image
              src={image}
              alt={title}
              width={100}
              height={100}
              className="rounded-xl"
            />
          </div>
        )}
        <div className="flex flex-col gap-3">
          <Text size="base" state="primary" type="medium">
            {title}
          </Text>
          <Text size="sm" state="secondary" type="regular">
            {date}
          </Text>
        </div>
      </Link>
    </div>
  )
}

export default CardBlog
