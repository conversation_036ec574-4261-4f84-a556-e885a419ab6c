import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"
import { useModalStore } from "stores/modalStore"

import BuyingDashboardHistoryDetailModalContent from "./BuyingDashboardHistoryDetailModalContent"

const { BUYING_HISTORY_DETAIL } = ModalConstant.MODAL_IDS

const BuyingDashboardHistoryDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setSelectedRowKeys } = useBuyingInProgressStore()

  const handleClose = () => {
    setOpen(false, BUYING_HISTORY_DETAIL)
    setSelectedRowKeys([])
  }

  if (!open || modalId !== BUYING_HISTORY_DETAIL) return null

  return (
    <Modal
      modalId={BUYING_HISTORY_DETAIL}
      onClose={handleClose}
      className="!min-h-[612px]"
    >
      <HeaderModal onClose={handleClose} title="Order Detail" />
      <BuyingDashboardHistoryDetailModalContent />
    </Modal>
  )
}

export default BuyingDashboardHistoryDetailModal
