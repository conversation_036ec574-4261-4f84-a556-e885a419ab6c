"use client"

import { useInView } from "react-intersection-observer"
import { useEffect } from "react"

import CenterWrapper from "@shared/CenterWrapper"
import ProductList from "@shared/ProductList"
import { isHttpNotFoundError } from "@utils/network"
import { useSearchStore } from "stores/searchStore"
import SearchLoading from "@components/ExpandedSearch/SearchLoading"

import EmptyCollection from "./EmptyCollection"

export interface CollectionResultProps {
  fetchNextPage: () => void
  hasNextPage: boolean
  isFetchingNextPage: boolean
}

const CollectionResult = ({
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
}: CollectionResultProps) => {
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: "300px",
    trackVisibility: true,
    delay: 100,
  })
  const { products, isLoading, error, productWishlist, setProductWishlist } =
    useSearchStore()

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage])

  if (isLoading) {
    return <SearchLoading />
  }

  if (isHttpNotFoundError(error) || !products?.length) {
    return <EmptyCollection />
  }

  return (
    <CenterWrapper className="!p-0">
      <ProductList
        list={products}
        containerProps={{ className: "col-span-2 md:col-span-3" }}
        productWishlist={productWishlist}
        onUnWishlisted={(_, product) => setProductWishlist(product.id, 0)}
        onWishlistAdded={(wishlistIds, product) =>
          setProductWishlist(product.id, wishlistIds?.[0] || 0)
        }
      />

      {/* Sentinel element for infinite scroll */}
      <div
        ref={ref}
        className="h-10 w-full"
        style={{ visibility: hasNextPage ? "visible" : "hidden" }}
      >
        {isFetchingNextPage && (
          <div className="flex justify-center py-4">
            <span className="loading loading-spinner loading-md" />
          </div>
        )}
      </div>
    </CenterWrapper>
  )
}

export default CollectionResult
