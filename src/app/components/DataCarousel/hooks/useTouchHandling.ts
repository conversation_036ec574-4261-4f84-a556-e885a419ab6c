import { useCallback, useEffect, useState } from "react"

const useTouchHandling = (
  scrollContainerRef: React.RefObject<HTMLDivElement>,
) => {
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)

  const handleTouchStart = useCallback(
    (e: React.TouchEvent<HTMLDivElement> | TouchEvent) => {
      if (!scrollContainerRef.current) return
      const touch =
        (e as TouchEvent).touches?.[0] ??
        (e as React.TouchEvent<HTMLDivElement>).touches[0]
      setIsDragging(true)
      setStartX(touch.clientX - scrollContainerRef.current.offsetLeft)
      setScrollLeft(scrollContainerRef.current.scrollLeft)
    },
    [scrollContainerRef],
  )

  const handleTouchMove = useCallback(
    (e: React.TouchEvent<HTMLDivElement> | TouchEvent) => {
      if (!isDragging || !scrollContainerRef.current) return
      e.preventDefault()
      const touch =
        (e as TouchEvent).touches?.[0] ??
        (e as React.TouchEvent<HTMLDivElement>).touches[0]
      const x = touch.clientX - scrollContainerRef.current.offsetLeft
      const walk = (x - startX) * 2
      scrollContainerRef.current.scrollLeft = scrollLeft - walk
    },
    [isDragging, scrollLeft, startX, scrollContainerRef],
  )

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false)
  }, [])

  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const domTouchStart = (e: TouchEvent) => handleTouchStart(e)
    const domTouchMove = (e: TouchEvent) => handleTouchMove(e)
    const domTouchEnd = () => handleTouchEnd()

    container.addEventListener("touchstart", domTouchStart)
    container.addEventListener("touchmove", domTouchMove)
    container.addEventListener("touchend", domTouchEnd)

    return () => {
      container.removeEventListener("touchstart", domTouchStart)
      container.removeEventListener("touchmove", domTouchMove)
      container.removeEventListener("touchend", domTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, scrollContainerRef])

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  }
}

export default useTouchHandling
