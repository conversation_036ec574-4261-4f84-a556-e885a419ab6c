import { create } from "zustand"

import { PaymentCreditDebitFormValues } from "@components/Payment/schema/paymentCreditDebitSchema"
import { BINData } from "types/bin.type"

interface State {
  form: PaymentCreditDebitFormValues
  binData: BINData | null
}

interface Actions {
  resetForm: () => void
  setForm: (form: PaymentCreditDebitFormValues) => void
  setBinData: (binData: BINData) => void
}

const initialState = {
  form: {
    cardNumber: "",
    cardExpMonth: "",
    cardExpYear: "",
    cardCvv: "",
    isAgreed: false,
  } as PaymentCreditDebitFormValues,
  binData: null,
}

export const usePaymentCardFormStore = create<State & Actions>((set) => ({
  ...initialState,
  resetForm: () => set(initialState),
  setForm: (form: PaymentCreditDebitFormValues) => set({ form }),
  setBinData: (binData: BINData) => set({ binData }),
}))
