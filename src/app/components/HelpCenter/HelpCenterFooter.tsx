import { Divider } from "@kickavenue/ui/dist/src/components"

import HelpCenterContact from "./HelpCenterContact"

interface HelpCenterFooterProps {
  className?: string
}

const HelpCenterFooter = ({ className }: HelpCenterFooterProps) => {
  const currentYear = new Date().getFullYear()
  return (
    <div className={className}>
      <Divider orientation="horizontal" />
      <HelpCenterContact />
      <Divider orientation="horizontal" />
      <div className="flex justify-center py-lg">
        <p className="m-0 text-sm text-gray">
          &copy; {currentYear} Kick Avenue. All Rights Reserved.{" "}
        </p>
      </div>
    </div>
  )
}

export default HelpCenterFooter
