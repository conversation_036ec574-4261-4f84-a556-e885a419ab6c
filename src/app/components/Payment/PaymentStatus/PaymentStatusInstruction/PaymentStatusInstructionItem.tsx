import { useCallback, useMemo, useState } from "react"
import { IconArrowDownOutline } from "@kickavenue/ui/components/icons"
import Text from "@kickavenue/ui/components/Text"
import Divider from "@kickavenue/ui/components/Divider/Divider"
import Space from "@kickavenue/ui/components/Space"

import ClickableDiv from "@components/shared/ClickableDiv"
import { TPaymentInstruction } from "types/payment.typs"

const PaymentStatusInstructionItem = ({
  instruction,
}: {
  instruction: TPaymentInstruction
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const chevronIconStyle = useMemo(() => {
    return isExpanded ? "rotate-180" : ""
  }, [isExpanded])

  const toggleExpand = useCallback(() => {
    setIsExpanded(!isExpanded)
  }, [isExpanded, setIsExpanded])

  return (
    <div className="">
      <div className="flex justify-between">
        <ClickableDiv
          keyDownHandler={toggleExpand}
          onClick={toggleExpand}
          className="flex grow items-center"
        >
          <Text size="base" type="bold" state="primary">
            {instruction?.title}
          </Text>
        </ClickableDiv>
        <IconArrowDownOutline
          onClick={toggleExpand}
          className={`size-5 cursor-pointer text-gray-w-40 transition-transform ${chevronIconStyle}`}
        />
      </div>
      {isExpanded && (
        <div className="px-base pt-base">
          {instruction?.items?.map((item, idx) => (
            <Text key={item} size="base" type="regular" state="secondary">
              <div className="flex gap-xs">
                <div className="">{idx + 1}.</div>
                <div className="">{item}</div>
              </div>
            </Text>
          ))}
        </div>
      )}
      <Space size="base" direction="y" type="margin" />
      <Divider orientation="horizontal" state="default" />
    </div>
  )
}

export default PaymentStatusInstructionItem
