/* eslint-disable @typescript-eslint/naming-convention */

import { NextRequest, NextResponse } from "next/server"

import { apiHttpClient } from "@infrastructure/providers/apiHttpClient"

export async function POST(request: NextRequest) {
  try {
    const { provider, token } = await request.json()

    if (!provider || !token) {
      return NextResponse.json(
        {
          code: 400,
          status: "error",
          message: "Provider and token are required",
          data: null,
        },
        { status: 400 },
      )
    }

    const response = await apiHttpClient.post(
      "/api/v1/auth/verify-cognito-token",
      {
        provider,
        token,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )

    return NextResponse.json(response.data)
  } catch (error: any) {
    return NextResponse.json(
      {
        code: error.response?.status || 500,
        status: "error",
        message: error.response?.data?.message || "Failed to verify token",
        data: null,
      },
      { status: error.response?.status || 500 },
    )
  }
}
