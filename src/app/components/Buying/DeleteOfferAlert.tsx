import { Alert } from "@kickavenue/ui/components"

interface DeleteOfferAlertProps {
  selectedRowKeys: number[] | undefined
}

const DeleteOfferAlert = ({ selectedRowKeys }: DeleteOfferAlertProps) => {
  const text = `Please review the ${selectedRowKeys?.length || 0} offers listed below before confirming deletion.`
  return (
    <div className="mb-sm px-lg md:mb-lg">
      <Alert className="!w-full" variant="information" isIcon subTitle={text} />
    </div>
  )
}

export default DeleteOfferAlert
