import {
  Avatar,
  Button,
  DropdownItemProps,
  IconArrowDownOutline,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"

import { listSideBar } from "@components/Profile/sidebar.utils"
import DropdownDynamicChild from "@components/shared/Form/DropdownDynamicChild"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { getMemberFullName } from "@utils/member.utils"
import { TMember, TMemberAuthStatus } from "types/member.type"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useMemberStore } from "stores/memberStore"

const { Authenticated, Unauthenticated } = TMemberAuthStatus

const AvatarProfile = () => {
  const router = useRouter()
  const { status } = useSession()
  const { member } = useMemberStore()
  const { isLoading, goToPage } = useChangePage()

  const options = listSideBar.map((item) => ({
    text: item.title,
    value: item.title,
    iconLeading: item.icon,
  }))

  let avatarUrl = ""
  avatarUrl = member?.image ? convertS3UrlToCloudFront(member.image) : ""

  const handleOnItemSelect = (opt: DropdownItemProps) => {
    const item = listSideBar.find((i) => i.title === opt.value)
    if (!item) return
    router.push(item.pathname)
  }

  if (status === Unauthenticated) {
    return (
      <div className="flex gap-xs">
        <Button
          variant="secondary"
          size="md"
          disabled={isLoading[PageRouteConstant.LOGIN]}
          onClick={() => goToPage(PageRouteConstant.LOGIN)}
        >
          Login
        </Button>
        <Button
          variant="primary"
          size="md"
          disabled={isLoading[PageRouteConstant.REGISTER]}
          onClick={() => goToPage(PageRouteConstant.REGISTER)}
        >
          Register
        </Button>
      </div>
    )
  }

  if (status === Authenticated) {
    return (
      <div className="flex items-center gap-xs">
        <Avatar
          name={getMemberFullName(member as TMember)}
          url={avatarUrl}
          size="md"
        />
        <DropdownDynamicChild
          options={options}
          placement="rightBottom"
          onItemSelect={handleOnItemSelect}
          optionClassName="[&>button]:!py-xs [&>button]:!px-sm !z-30"
          optionStyle={{ top: "30px" }}
        >
          <IconArrowDownOutline />
        </DropdownDynamicChild>
      </div>
    )
  }

  return <>...</>
}

export default AvatarProfile
