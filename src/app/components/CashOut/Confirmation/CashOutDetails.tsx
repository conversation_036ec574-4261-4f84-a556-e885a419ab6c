"use client"

import { IconDangerCircleBulk, Input, Text } from "@kickavenue/ui/components"
import { Controller, useFormContext } from "react-hook-form"
import { useCallback } from "react"

import { formatNumberWithSeparator } from "@utils/separator"

const CashOutDetails = () => {
  const {
    control,
    formState: { errors },
    clearErrors,
    setValue,
    trigger,
  } = useFormContext()

  const handleAmountChange = useCallback(
    (value: string) => {
      // Remove non-numeric characters and convert to string
      const numericValue = Number(value.replace(/[^0-9]/g, ""))
      setValue("amount", String(numericValue))
      trigger("amount")
    },
    [setValue, trigger],
  )

  const formatInputValue = (value: string) => {
    const formatted = formatNumberWithSeparator(value || 0, ",")
    return formatted === "0" ? "" : formatted
  }

  return (
    <div className="flex flex-col gap-3">
      <Text size="sm" type="bold" state="primary">
        Cash Out Amount
      </Text>
      <Controller
        name="amount"
        control={control}
        render={({ field }) => (
          <div className="flex flex-col gap-2">
            <Input
              prefix="IDR"
              placeholder="Input Amount"
              value={formatInputValue(field.value)}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                handleAmountChange(e.target.value)
              }}
              variant={errors.amount && "danger"}
              rightIcon={
                errors.amount && (
                  <IconDangerCircleBulk
                    className="cursor-pointer"
                    onClick={() => {
                      // Clear the error when user clicks on the icon
                      handleAmountChange("")
                      clearErrors("amount")
                    }}
                  />
                )
              }
            />
            {errors.amount?.message && (
              <Text size="xs" state="danger" type="regular">
                {errors.amount?.message as string}
              </Text>
            )}
          </div>
        )}
      />
    </div>
  )
}

export default CashOutDetails
