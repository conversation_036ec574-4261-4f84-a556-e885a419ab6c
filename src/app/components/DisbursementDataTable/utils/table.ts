import { GetMyDisbursementData } from "types/disbursement.type"

export const prettierDisbursementData = (
  disbursementData?: GetMyDisbursementData,
): GetMyDisbursementData => {
  if (!disbursementData) return {} as GetMyDisbursementData
  return {
    ...disbursementData,

    // CashIn can be as TopUp
    // type:
    //   disbursementData.type === EDisbursementActionNameType.CashIn
    //     ? EDisbursementActionNameType.TopUp
    //     : disbursementData.type,
  }
}

export const prettierDisbursementTable = (
  disbursementData?: GetMyDisbursementData[],
): GetMyDisbursementData[] => {
  if (!disbursementData) return []
  return disbursementData.map((item: GetMyDisbursementData) => ({
    ...prettierDisbursementData(item),
  }))
}

export const defaultDisbursementFilter = () => ({
  page: 0,
  pageSize: 10,
})
