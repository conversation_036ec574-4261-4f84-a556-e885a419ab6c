import { useEffect } from "react"

import SizeListTab from "@components/shared/SizeList/SizeListTab"
import { formatStripePrice, getSizeTypeLabel } from "@utils/misc"
import { useProductStore } from "stores/productStore"
import { TStripePrice } from "types/stripe.type"
import { useMiscStore } from "stores/miscStore"
import ChipSell from "@components/shared/Chip/ChipSell"

import useSellAskSizeList from "./hook/useSizeList"

const SellAskSizeList = () => {
  const { sizeType, setSizeType, detail: product } = useProductStore()
  const { setShowFooter } = useMiscStore()

  const { getChipState, handleChipHovered, getChipType, handleChipClick } =
    useSellAskSizeList()

  useEffect(() => {
    setShowFooter(false)
    return () => {
      setShowFooter(true)
    }
  }, [setShowFooter])

  return (
    <div className="">
      <SizeListTab sizeType={sizeType} setSizeType={setSizeType} />
      <div className="grid grid-cols-4 gap-base md:grid-cols-12">
        {product?.highestOffer?.map((offer) => {
          return (
            <ChipSell
              className="col-span-2 !size-full min-w-[121px] md:col-span-3"
              key={offer.size.id}
              size={`${getSizeTypeLabel(sizeType, offer.size)} ${offer.size[sizeType]}`}
              price={formatStripePrice(
                offer.highestOffer as TStripePrice,
                "IDR",
              )}
              type={getChipType(offer)}
              state={getChipState(offer)}
              onMouseEnter={() => handleChipHovered(true, offer)}
              onMouseLeave={() => handleChipHovered(false, offer)}
              onClick={() => handleChipClick(offer)}
            />
          )
        })}
      </div>
    </div>
  )
}

export default SellAskSizeList
