import { Heading } from "@kickavenue/ui/components/index"

import { getPaymentInstructions } from "../paymentStatus.utils"

import PaymentStatusInstructionItem from "./PaymentStatusInstructionItem"

const PaymentStatusInstruction = () => {
  const instructions = getPaymentInstructions()

  return (
    <div className="md:flex md:justify-center">
      <div className="rounded-base bg-white p-lg pb-sm md:w-[564px]">
        <div className="flex w-full flex-col gap-base">
          <Heading heading="5" textStyle="bold">
            Payment Instruction
          </Heading>
          {instructions?.map((item) => (
            <PaymentStatusInstructionItem key={item.title} instruction={item} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default PaymentStatusInstruction
