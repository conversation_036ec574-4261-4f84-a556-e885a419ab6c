import Image from "next/image"
import Link from "next/link"
import React from "react"
import { Text } from "@kickavenue/ui/components"

const CardBlogCategory = () => {
  return (
    <div className="bg-base-100 shadow-xl">
      <Link href="" className="flex flex-col gap-3">
        <Image
          src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          alt=""
          width={100}
          height={100}
          className="!w-full rounded-xl"
        />
        <div className="flex flex-col gap-3">
          <Text size="base" state="primary" type="medium">
            asd
          </Text>
          <Text size="sm" state="secondary" type="regular">
            asd
          </Text>
        </div>
      </Link>
    </div>
  )
}
export default CardBlogCategory
