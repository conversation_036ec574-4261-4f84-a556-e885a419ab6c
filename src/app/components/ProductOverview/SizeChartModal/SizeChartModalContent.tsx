import { useInView } from "react-intersection-observer"
import { Text } from "@kickavenue/ui/dist/src/components"
import { useEffect } from "react"

import SpinnerLoading from "@components/shared/SpinnerLoading"
import { Product } from "types/product.type"
import { MiscConstant } from "@constants/misc"
import Spinner from "@components/shared/Spinner"

import SizeChartModalTable from "./SizeChartModalTable"
import useFetchInfiniteSizeChart from "./hook/useFetchInfiniteSizeChart"

const { PAGE, PAGE_SIZE } = MiscConstant.PAGING_DEFAULT

const SizeChartModalContent = ({ item }: { item: Product }) => {
  const { data, isLoading, query } = useFetchInfiniteSizeChart({
    brandId: item.brands?.[0]?.id,
    page: PAGE,
    pageSize: PAGE_SIZE,
    sort: [],
  })
  const { ref, inView } = useInView()

  useEffect(() => {
    if (inView) {
      query.fetchNextPage()
    }
  }, [inView, query])

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  return (
    <div className="max-h-[612px] overflow-y-auto px-lg pt-lg">
      {data?.map((sizeChart) => (
        <div key={sizeChart.id} className="mb-lg flex flex-col gap-lg">
          <Text key={sizeChart.id} size="base" type="bold" state="primary">
            {sizeChart?.name || "-"}
          </Text>
          <SizeChartModalTable sizeChart={sizeChart} />
        </div>
      ))}
      {query.hasNextPage && (
        <div ref={ref} className="flex justify-center">
          <Spinner />
        </div>
      )}
    </div>
  )
}

export default SizeChartModalContent
