"use client"

import { cx } from "class-variance-authority"

import useExpandedSearch from "@app/hooks/useExpandedSearch"

import ExpandedSearchContent from "./ExpandedSearchContent"
import SearchResultPreview from "./SearchResultPreview"

const ExpandedSearchOverlay = () => {
  const {
    showExpandedSearch,
    showSearchResultPreview,
    handleClose,
    handleKeyDown,
  } = useExpandedSearch()
  const renderContent = showSearchResultPreview ? (
    <SearchResultPreview />
  ) : (
    <ExpandedSearchContent />
  )
  return (
    <div
      className={cx(
        "absolute inset-0 z-20",
        "transition-all duration-[0.3s] ease-in-out",
        showExpandedSearch && "top-[65px] translate-y-0 opacity-100",
        !showExpandedSearch &&
          "top-0 -translate-y-full overflow-hidden opacity-0",
      )}
    >
      {renderContent}
      <div
        className="size-full bg-black-dim-40"
        onClick={handleClose}
        onFocus={handleClose}
        onKeyDown={handleKeyDown}
        aria-label="Close expanded search"
      />
    </div>
  )
}

export default ExpandedSearchOverlay
