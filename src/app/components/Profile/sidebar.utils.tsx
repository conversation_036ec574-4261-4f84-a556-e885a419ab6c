import {
  IconProfileOutline,
  IconBuyingDashboardOutline,
  IconSellingOutline,
  IconConsignmentOutline,
  IconWishlistOutline,
  IconTicket,
  IconAddFriendOutline,
  IconNotificationOutline,
  IconSettingOutline,
} from "@kickavenue/ui/components/icons"
import { ReactNode } from "react"

export interface ListSideBar {
  icon: ReactNode
  title: string
  subTitle: string
  pathname: string
}
export const listSideBar: ListSideBar[] = [
  {
    icon: <IconProfileOutline />,
    title: "Profile",
    subTitle: "See and update your personal information",
    pathname: "/profile",
  },
  {
    icon: <IconBuyingDashboardOutline />,
    title: "Buying",
    subTitle: "Check your buying activity and order history.",
    pathname: "/profile/buying",
  },
  {
    icon: <IconSellingOutline />,
    title: "Selling",
    subTitle: "Manage your sales and view selling history.",
    pathname: "/profile/selling",
  },
  {
    icon: <IconConsignmentOutline />,
    title: "Consignment",
    subTitle: "Track your consigned items and their status",
    pathname: "/profile/consignment",
  },
  {
    icon: <IconWishlistOutline />,
    title: "Wishlist",
    subTitle: "See what items you have saved",
    pathname: "/profile/wishlist",
  },
  {
    icon: <IconTicket />,
    title: "My Voucher",
    subTitle: "See what vouchers you have",
    pathname: "/profile/my-voucher",
  },
  {
    icon: <IconAddFriendOutline />,
    title: "Invite and Earn",
    subTitle: "Invite your friends and get a reward",
    pathname: "/profile/invite-and-earn",
  },
  {
    icon: <IconNotificationOutline />,
    title: "Message Center",
    subTitle: "See the latest news",
    pathname: "/profile/message-center",
  },
  {
    icon: <IconSettingOutline />,
    title: "Settings",
    subTitle: "Manage your general settings and preferences",
    pathname: "/profile/settings",
  },
]

export const isRouterSelected = (pathname: string, currentPathName: string) => {
  return pathname === currentPathName
}
