import {
  IconLocationSuccessBulkColor,
  Divider,
  Text,
  Badge,
  IconLocationDangerBulk,
  Button,
  Space,
} from "@kickavenue/ui/components"

import { useModalStore } from "stores/modalStore"
import { TAddress } from "types/address.type"
import FormAddresses from "@components/Profile/saved-addresses/FormAddresses"
import FormEditAddresses from "@components/Profile/saved-addresses/FormEditAddresses"
import ModalDelete from "@components/Profile/saved-addresses/ModalDelete"
import { ModalConstant } from "@constants/modal"
import { getMemberFullName } from "@utils/member.utils"
import { useMemberStore } from "stores/memberStore"

import { useCheckoutAddress } from "../hooks/useCheckoutAddress"

import DropdownAddress from "./DropdownAddress"
import { ChangeAddress } from "./ChangeAddress"

const { NEW_ADDRESS } = ModalConstant.MODAL_IDS

export default function CardAddress() {
  const { setOpen } = useModalStore()
  const { selectedAddress, provinces, cities, countries, location } =
    useCheckoutAddress()
  const { member } = useMemberStore()

  const renderAddress = () => {
    const { addressDetail, phoneNumber, title } =
      selectedAddress || ({} as TAddress)
    return (
      <div className="rounded-xl border border-solid border-gray-w-80 bg-white">
        <FormEditAddresses
          countries={countries}
          cities={cities}
          provinces={provinces}
        />
        <ModalDelete />
        <div className="flex items-center gap-x-xs p-sm">
          <IconLocationSuccessBulkColor className="size-5 scale-100" />
          <Text size="sm" state="primary" type="bold">
            {title}
          </Text>
          <div className="size-1 rounded-full bg-black" />
          <Text size="sm" state="primary" type="bold">
            {getMemberFullName(member)}
          </Text>
          <div className="grow">
            {selectedAddress?.isPrimary && (
              <Badge size="md" text="Default" type="information" />
            )}
          </div>

          <DropdownAddress address={selectedAddress} />
        </div>
        <Divider orientation="horizontal" />
        <div className="p-sm">
          <Text size="sm" state="secondary" type="regular" className="py-xxs">
            {phoneNumber}
          </Text>
          <Text size="sm" state="secondary" type="regular">
            {addressDetail}
          </Text>
          <Text size="sm" state="secondary" type="regular">
            {location}
          </Text>
        </div>
      </div>
    )
  }

  const renderContent = () => {
    switch (selectedAddress) {
      case undefined:
        return (
          <div className="rounded-xl border border-solid border-gray-w-80 bg-white">
            <FormAddresses
              countries={countries}
              provinces={provinces}
              cities={cities}
            />
            <div className="flex items-center gap-x-xs p-sm">
              <IconLocationDangerBulk className="size-5 scale-100" />
              <Text size="sm" state="primary" type="bold">
                No Address Yet
              </Text>
              <div className="grow" />
            </div>
            <Divider orientation="horizontal" />
            <div className="p-sm">
              <Text size="sm" state="secondary" type="regular">
                Add your address to continue checkout
              </Text>
              <Space size="sm" direction="y" type="margin" />
              <Button
                size="md"
                variant="secondary"
                style={{ width: "100%" }}
                onClick={() => {
                  setOpen(true, NEW_ADDRESS)
                }}
              >
                Add
              </Button>
            </div>
          </div>
        )
      default:
        return renderAddress()
    }
  }

  return (
    <>
      {renderContent()}
      <ChangeAddress defaultAddress={selectedAddress} />
    </>
  )
}
