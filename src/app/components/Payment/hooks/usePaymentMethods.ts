import { getPaymentMethodByType } from "@utils/paymentMethod.utils"
import { useCheckoutStore } from "stores/checkoutStore"
import { usePaymentStore } from "stores/paymentStore"
import { EPaymentMethodType } from "types/paymentMethod.type"

const {
  VirtualAccount,
  Installments,
  EWallet,
  FullWallet,
  DebitCard,
  CreditCard,
} = EPaymentMethodType

const usePaymentMethods = () => {
  const { listPaymentMethod } = usePaymentStore()
  const { voucher } = useCheckoutStore()

  const VAPayments = getPaymentMethodByType(
    listPaymentMethod,
    VirtualAccount,
    voucher?.eligiblePaymentMethod,
  )
  const InstallmentPayments = getPaymentMethodByType(
    listPaymentMethod,
    Installments,
    voucher?.eligiblePaymentMethod,
  )
  const walletPayments = getPaymentMethodByType(
    listPaymentMethod,
    [EWallet, FullWallet],
    voucher?.eligiblePaymentMethod,
  )
  const cardPayments = getPaymentMethodByType(
    listPaymentMethod,
    [DebitCard, CreditCard],
    voucher?.eligiblePaymentMethod,
  )

  return {
    VAPayments,
    InstallmentPayments,
    walletPayments,
    cardPayments,
  }
}

export default usePaymentMethods
