import { Button, Space } from "@kickavenue/ui/components"

export interface ShowMoreButtonProps {
  showMore?: boolean
  onClick?: () => void
}

const ShowMoreButton = ({ showMore = false, onClick }: ShowMoreButtonProps) => {
  if (!showMore) return null

  return (
    <>
      <Space type="margin" direction="y" size="sm" />
      <Button
        variant="link"
        onClick={onClick}
        className="w-full justify-center !p-0"
      >
        Show More
      </Button>
    </>
  )
}

export default ShowMoreButton
