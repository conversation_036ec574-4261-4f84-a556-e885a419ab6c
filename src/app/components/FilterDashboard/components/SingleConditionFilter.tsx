import RadioButton from "@kickavenue/ui/components/RadioButton"

const SingleConditionFilter = ({
  selectedCondition,
  updateCondition,
}: {
  selectedCondition?: string[]
  updateCondition: (condition: string[]) => void
}) => {
  const isConditionSelected = (condition: string) => {
    return selectedCondition?.includes(condition) ?? false
  }

  const handleConditionChange = (condition: string) => {
    if (isConditionSelected(condition)) {
      updateCondition(selectedCondition?.filter((c) => c !== condition) ?? [])
      return
    }
    updateCondition([condition])
  }

  return (
    <div className="h-[300px] overflow-y-auto px-6 pt-6">
      <div className="flex flex-col gap-sm">
        <RadioButton
          checked={isConditionSelected("Brand New")}
          label="Brand New"
          name="condition"
          value="true"
          onChange={() => handleConditionChange("Brand New")}
        />
        <RadioButton
          checked={isConditionSelected("99% Perfect")}
          label="99% Perfect"
          name="condition"
          value="false"
          onChange={() => handleConditionChange("99% Perfect")}
        />
      </div>
    </div>
  )
}

export default SingleConditionFilter
