import { z } from "zod"

import {
  MAX_CASH_OUT_AMOUNT_ERROR,
  MIN_CASH_OUT_AMOUNT,
  MIN_CASH_OUT_AMOUNT_ERROR,
} from "../constants"

export const createCashOutSchema = (
  currentBalanceNumber: number,
  currentBalance: string,
) =>
  z.object({
    bankId: z.number().min(1, "Bank is required").nullable(),
    amount: z
      .string()
      .min(1, "Amount is required")
      .refine(
        (value) => Number(value) >= MIN_CASH_OUT_AMOUNT,
        MIN_CASH_OUT_AMOUNT_ERROR,
      )
      .refine(
        (value) => Number(value) <= currentBalanceNumber,
        `${MAX_CASH_OUT_AMOUNT_ERROR} ${currentBalance}`,
      ),
    selectedBank: z
      .object({
        id: z.number(),
        accountHolder: z.string(),
        accountNumber: z.string(),
      })
      .nullable(),
    password: z.string().min(1, "Password is required"),
  })

export type CashOutFormValues = z.infer<ReturnType<typeof createCashOutSchema>>
