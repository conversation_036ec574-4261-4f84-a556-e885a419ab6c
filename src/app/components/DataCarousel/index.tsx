"use client"

import React, {
  useRef,
  ReactNode,
  useState,
  useEffect,
  forwardRef,
} from "react"
import Carousel from "@kickavenue/ui/components/Carousel/Carousel"
import { TCarouselRef } from "@kickavenue/ui/components/Carousel/Carousel.type"

import useTouchHandling from "./hooks/useTouchHandling"
import useMouseHandling from "./hooks/useMouseHandling"

export interface DataItem {
  id: string | number
  [key: string]: any
}

interface DataCarouselProps<T extends DataItem> {
  data: T[]
  renderItem: (item: T) => ReactNode
  itemsPerSlide?: number
}

const CustomCarousel = forwardRef<TCarouselRef, any>((props, ref) => {
  const { children, ...rest } = props
  const carouselRef = useRef<HTMLDivElement>(null)

  return (
    <div ref={carouselRef} className="carousel-wrapper relative">
      <Carousel ref={ref} {...rest}>
        {children}
      </Carousel>
    </div>
  )
})

CustomCarousel.displayName = "CustomCarousel"

const DataCarousel = <T extends DataItem>({
  data,
  renderItem,
  itemsPerSlide = 5,
}: DataCarouselProps<T>) => {
  const ref = useRef<TCarouselRef>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const { handleTouchStart, handleTouchMove, handleTouchEnd } =
    useTouchHandling(scrollContainerRef)
  const { handleMouseDown, isMouseDragging } =
    useMouseHandling(scrollContainerRef)

  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  const effectiveItemsPerSlide = isMobile ? 2 : itemsPerSlide

  const slidesContent = data.reduce((slides: T[][], item, index) => {
    if (index % effectiveItemsPerSlide === 0) {
      slides.push([])
    }
    slides[slides.length - 1].push(item)
    return slides
  }, [])

  return (
    <div className="-mx-6 w-[calc(100%+48px)] md:-mx-12 md:w-[calc(100%+96px)]">
      <div
        ref={scrollContainerRef}
        className={`hide-scrollbar overflow-x-auto ${isMouseDragging ? "cursor-grabbing" : "cursor-grab"}`}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        style={{ scrollBehavior: "smooth" }}
      >
        <CustomCarousel
          ref={ref}
          size="lg"
          variant="rectangle"
          className="w-full"
          classNameNext="!right-0"
          classNamePrev="!left-0"
        >
          {slidesContent?.map((slide, slideIndex) => (
            // eslint-disable-next-line react/no-array-index-key
            <div key={`slide-${slideIndex}`} className="w-full px-12">
              <div
                className="grid w-full grid-cols-2 gap-3 md:grid-cols-3 md:gap-6 lg:grid-cols-4 xl:grid-cols-5"
                style={{
                  justifyContent: "stretch",
                }}
              >
                {slide.map((item) => (
                  <div key={item.id} className="w-full">
                    {renderItem(item)}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CustomCarousel>
      </div>
    </div>
  )
}

export default DataCarousel
