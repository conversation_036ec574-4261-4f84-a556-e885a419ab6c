import CheckboxHeader from "@components/SellingDashboard/CheckboxHeader"
import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import { adaptOfferToListingItem } from "@utils/listingItemAdapter"
import { formatCurrencyStripe, formatDateObj } from "@utils/misc"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { TPackagingConditionEnum, TItemConditionEnum } from "types/listing.type"
import { TOffer } from "types/offer.type"
import { TTableColumn } from "types/table.type"

import BuyingDashboardOfferStatus from "../BuyingDashboardOffer/BuyingDashboardOfferStatus"
import BuyingDashboardOfferRowActions from "../BuyingDashboardOffer/BuyingDashboardOfferRowActions"

const columnWidth = {
  checkbox: 44,
  productDetails: 300,
  size: 64,
  conditions: 108,
  offerPrice: 148,
  lowestAsk: 148,
  highestOffer: 148,
  expiryDate: 140,
  paymentMethod: 148,
  status: 128,
  actions: 67,
}

const useBuyingDashboardOfferTable = () => {
  const { selectedRowKeys, setSelectedRowKeys, buyingOfferData } =
    useBuyingOfferStore()

  const columns = [
    {
      key: "checkbox",
      title: "",
      width: columnWidth.checkbox,
      renderHeader: () => (
        <CheckboxHeader
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          data={buyingOfferData}
        />
      ),
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: columnWidth.productDetails,
      render: (record: TOffer) => {
        const adaptedItem = adaptOfferToListingItem(record)
        return <ProductDetailColumn listingItem={adaptedItem} />
      },
    },
    {
      key: "size",
      dataIndex: "size",
      title: "Size",
      width: columnWidth.size,
      render: (record: TOffer) => (
        <TitleSubTitleColumn title="US" subtitle={record?.size?.us || "-"} />
      ),
    },
    {
      key: "itemCondition",
      title: "Conditions",
      width: columnWidth.conditions,
      render: (record: TOffer) => (
        <ConditionColumn
          itemCondition={record?.listing?.itemCondition as TItemConditionEnum}
          packagingCondition={
            record?.listing?.packagingCondition as TPackagingConditionEnum
          }
        />
      ),
    },
    {
      key: "amount",
      title: "Offer Price",
      width: columnWidth.offerPrice,
      render: (record: TOffer) =>
        formatCurrencyStripe({ price: record?.amount }),
      sorter: () => {},
    },
    {
      key: "lowestAsk",
      title: "Lowest Ask",
      width: columnWidth.lowestAsk,
      render: () => "-",
    },
    {
      key: "highestOffer",
      title: "Highest Offer",
      width: columnWidth.highestOffer,
      render: () => "-",
    },
    {
      key: "expiredAt",
      title: "Expiry Date",
      width: columnWidth.expiryDate,
      render: (offer: TOffer) =>
        offer?.expiredAt ? formatDateObj(new Date(offer.expiredAt)) : "-",
      sorter: () => {},
    },
    {
      key: "status",
      title: "Status",
      width: columnWidth.status,
      render: (offer: TOffer) => <BuyingDashboardOfferStatus record={offer} />,
    },
    {
      key: "actions",
      title: "Actions",
      headerClassName: "[&>div]:justify-center",
      className: "[&>div]:justify-center",
      width: columnWidth.actions,
      render: (record: TOffer) => (
        <BuyingDashboardOfferRowActions record={record} />
      ),
    },
  ] as TTableColumn[]

  return { columns }
}

export default useBuyingDashboardOfferTable
