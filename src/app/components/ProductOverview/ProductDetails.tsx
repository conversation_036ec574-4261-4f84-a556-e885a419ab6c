import { Product } from "types/product.type"
import { formatCurrencyStripe, formatDate } from "@utils/misc"
import { TStripePrice } from "types/stripe.type"

import ProductDetailItem from "./ProductDetailItem"

export interface ProductDetailsProps {
  product: Product
}

const ProductDetails = ({ product }: ProductDetailsProps) => {
  const formattedReleaseDate = product?.releaseDate
    ? formatDate(product.releaseDate)
    : "-"
  return (
    <div className="mb-lg grid grid-cols-4 gap-lg md:grid-cols-12">
      <div className="col-span-4 flex gap-base md:col-span-6">
        <ProductDetailItem title="SKU" value={product?.skuCode || ""} />
        <ProductDetailItem title="Color" value={product?.colorway || ""} />
      </div>
      <div className="col-span-4 flex gap-base md:col-span-6">
        <ProductDetailItem title="Release Date" value={formattedReleaseDate} />
        <ProductDetailItem
          title="Retail"
          value={formatCurrencyStripe({
            price: product?.retailPrice as TStripePrice,
          })}
          showDivider={false}
        />
      </div>
    </div>
  )
}

export default ProductDetails
