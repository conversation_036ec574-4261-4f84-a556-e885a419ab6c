import { Button, CheckBox, Text } from "@kickavenue/ui/components"
import React from "react"

import { useRegistrationStore } from "@stores/registrationStore"

const TermsCheckbox: React.FC = () => {
  const { registrationData, setRegistrationData } = useRegistrationStore()

  const handleTermsCheck = (checked: boolean) => {
    setRegistrationData({ isTermsChecked: checked })
  }

  return (
    <>
      {/* eslint-disable-next-line jsx-a11y/label-has-for */}
      <label
        id="terms-checkbox"
        htmlFor="terms-checkbox"
        className="flex text-sm"
      >
        <CheckBox
          id="terms-checkbox"
          className="mr-2 !flex items-center justify-center"
          checked={registrationData.isTermsChecked}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleTermsCheck(e.target.checked)
          }
        />
        <div className="flex flex-wrap gap-1">
          <Text size="sm" type="regular" state="secondary">
            By registering, I agree to Kick Avenue&apos;s{" "}
          </Text>
          <Button variant="link" className="!p-0">
            Terms and Conditions
          </Button>
          <Text size="sm" type="regular" state="secondary">
            and
          </Text>
          <Button variant="link" className="!p-0">
            Privacy Policy
          </Button>
        </div>
      </label>
    </>
  )
}

export default TermsCheckbox
