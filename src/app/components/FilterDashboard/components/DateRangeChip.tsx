import React from "react"
import { Chip } from "@kickavenue/ui/components"

interface DateRangeChipProps {
  keyName: string
  start: string
  end: string
  formatDateForChip: (dateString: string) => string
  handleRemoveChip: (key: string, item?: string) => void
}

const DateRangeChip: React.FC<DateRangeChipProps> = ({
  keyName,
  start,
  end,
  formatDateForChip,
  handleRemoveChip,
}) => {
  if (start && end) {
    return (
      <Chip
        size="sm"
        isRemovable
        className="!mb-4 whitespace-nowrap"
        onRemove={() => {
          handleRemoveChip(`${keyName}Start`)
          handleRemoveChip(`${keyName}End`)
        }}
      >
        {`${formatDateForChip(start)} - ${formatDateForChip(end)}`}
      </Chip>
    )
  } else if (start) {
    return (
      <Chip
        size="sm"
        isRemovable
        className="!mb-4 whitespace-nowrap"
        onRemove={() => handleRemoveChip(`${keyName}Start`)}
      >
        {`From ${formatDateForChip(start)}`}
      </Chip>
    )
  } else if (end) {
    return (
      <Chip
        size="sm"
        isRemovable
        className="!mb-4 whitespace-nowrap"
        onRemove={() => handleRemoveChip(`${keyName}End`)}
      >
        {`Until ${formatDateForChip(end)}`}
      </Chip>
    )
  }
  return null
}

export default DateRangeChip
