import React from "react"
import { Chip } from "@kickavenue/ui/components"

interface SingleValueChipProps {
  keyName: string
  value: string
  handleRemoveChip: (key: string, item?: string) => void
}

const SingleValueChip: React.FC<SingleValueChipProps> = ({
  keyName,
  value,
  handleRemoveChip,
}) => (
  <Chip
    size="sm"
    isRemovable
    className="mb-4 whitespace-nowrap"
    onRemove={() => handleRemoveChip(keyName)}
  >
    {value}
  </Chip>
)

export default SingleValueChip
