import Statistic from "@kickavenue/ui/components/Statistic"
import { useMemo } from "react"

import { formatPrice } from "@utils/misc"
import { TProductHistorical } from "types/transactionDetail.type"

export interface TProductStatisticProps {
  data: TProductHistorical
}

const ProductStatistic = ({ data }: TProductStatisticProps) => {
  const pricePremium = useMemo(() => {
    const value = data?.summary?.pricePremium
    if (!value || value === "-") return "-"
    if (value.includes("%")) return value
    return `${value}%`
  }, [data?.summary?.pricePremium])
  return (
    <div className="grid grid-cols-4 gap-sm md:grid-cols-12 md:gap-0">
      <div className="col-span-4 flex rounded-sm bg-gray-w-95 md:col-span-6 md:rounded-none md:rounded-l-sm">
        <div className="flex w-full justify-center border-r border-gray-w-80 p-sm">
          <Statistic
            type="basic"
            title="Lowest Sale"
            className="[&>div]:!text-center"
            content={formatPrice(data?.summary?.lowestSale, null, "IDR")}
          />
        </div>
        <div className="flex w-full justify-center border-gray-w-80 p-sm md:border-r">
          <Statistic
            type="basic"
            title="Average Sale"
            className="[&>div]:!text-center"
            content={formatPrice(data?.summary?.averageSale, null, "IDR")}
          />
        </div>
      </div>
      <div className="col-span-4 flex rounded-sm bg-gray-w-95 md:col-span-6 md:rounded-none md:rounded-r-sm">
        <div className="flex w-full justify-center border-r border-gray-w-80 p-sm">
          <Statistic
            type="basic"
            title="Highest Sale"
            className="[&>div]:!text-center"
            content={formatPrice(data?.summary?.highestSale, null, "IDR")}
          />
        </div>
        <div className="flex w-full justify-center p-sm">
          <Statistic
            type="basic"
            title="Price Premium"
            className="[&>div]:!text-center"
            content={pricePremium}
          />
        </div>
      </div>
    </div>
  )
}

export default ProductStatistic
