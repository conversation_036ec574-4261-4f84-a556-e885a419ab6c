import { signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useQueryClient } from "@tanstack/react-query"

import { AuthService } from "@application/services/authService"
import { useMemberStore } from "stores/memberStore"
import { useSearchStore } from "stores/searchStore"
import { TMember } from "types/member.type"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { tokenManager } from "@infrastructure/auth/tokenManager"
import { event } from "@lib/gtag"

import useFormState from "./useFormState"
import useAuthHandlers from "./useAuthHandlers"
import useSocialLogin from "./useSocialLogin"

const useAuthService = () => new AuthService()

export function useLoginForm() {
  const {
    email,
    setEmail,
    password,
    setPassword,
    error,
    setError,
    isLoading,
    setIsLoading,
  } = useFormState()
  const router = useRouter() as any
  const authService = useAuthService()
  const { setMember, member } = useMemberStore()
  const { clearProductWishlist } = useSearchStore()
  const queryClient = useQueryClient()

  const isFormValid = email.trim() !== "" && password.trim() !== ""

  const handleEmailChange = (newEmail: string) => setEmail(newEmail)
  const handlePasswordChange = (newPassword: string) => setPassword(newPassword)

  const { handleSubmit } = useAuthHandlers({
    email,
    password,
    setError,
    setIsLoading,
    authService,
    router,
  })

  const { handleSocialLogin } = useSocialLogin({ setError, setIsLoading })

  const handleSignOut = async () => {
    setIsLoading(true)
    setError("")

    try {
      event({
        action: "user_logged_out",
        params: {
          email: member?.email || "",
          province: member.provinceName,
        },
        userId: member?.id || "",
      })
      setMember({} as TMember)

      // Clear wishlist data from the Zustand store
      clearProductWishlist()

      // Clear React Query cache for wishlist data and logged-in member
      queryClient.removeQueries({
        queryKey: [
          QueryKeysConstant.GET_MY_WISHLIST,
          QueryKeysConstant.GET_LOGGED_IN_MEMBER,
        ],
      })

      // Clear all cache React Query
      queryClient.clear()

      // Sign out and redirect
      await signOut({ redirect: false })
      tokenManager.destroy()
      router.push("/login")
    } catch (err) {
      setError(
        `An unexpected error occurred during sign out. Please try again. Details: ${
          err instanceof Error ? err.message : String(err)
        }`,
      )
    } finally {
      setIsLoading(false)
    }
  }

  return {
    email,
    password,
    error,
    isLoading,
    isFormValid,
    handleEmailChange,
    handlePasswordChange,
    handleSubmit,
    handleSocialLogin,
    handleSignOut,
  }
}
