import { MiscConstant } from "@constants/misc"
import useURLQuery from "@app/hooks/useUrlQuery"
import { useBuyingHistoryStore } from "stores/buyingHistoryStore"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT } = MiscConstant.PAGING_DEFAULT

const useBuyingHistoryPagination = () => {
  const { filter } = useBuyingHistoryStore()
  const { handleChangeQuery } = useURLQuery()

  const page = filter?.page || 0
  const pageSize = filter?.pageSize || 10
  const totalPages = filter?.totalPages || 1

  const handleNext = () => {
    const nextPage = page + 1
    handleChangeQuery(PAGE, nextPage.toString())
  }

  const handlePrev = () => {
    const prevPage = page - 1
    handleChangeQuery(PAGE, prevPage.toString())
  }

  const handlePage = (page: number) => {
    handleChangeQuery(PAGE, (page - 1).toString())
  }

  const handlePerPage = (size: number) => {
    handleChangeQuery(PAGE_SIZE, size.toString())
    handleChangeQuery(PAGE, PAGE_DEFAULT)
  }

  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useBuyingHistoryPagination
