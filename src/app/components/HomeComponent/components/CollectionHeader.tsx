import Image from "next/image"
import Link from "next/link"
import React from "react"

import { convertS3UrlToCloudFront } from "@utils/misc"
import { IMenuWizardSection } from "types/menuWizard"

interface CollectionHeaderProps {
  redirectionUrl: string
  data: IMenuWizardSection
}

const CollectionHeader: React.FC<CollectionHeaderProps> = ({
  redirectionUrl,
  data,
}) => (
  <Link href={redirectionUrl}>
    <div className="relative aspect-[16/9] h-[450px] w-full">
      <Image
        src={convertS3UrlToCloudFront(data.sectionContent?.imagePortrait || "")}
        fill
        style={{ objectFit: "cover" }}
        alt={data.title}
        className="block md:hidden"
      />
      <Image
        src={convertS3UrlToCloudFront(
          data.sectionContent?.imageLandscape || "",
        )}
        fill
        style={{ objectFit: "cover" }}
        alt={data.title}
        className="hidden md:block"
      />
    </div>
  </Link>
)

export default CollectionHeader
