/* eslint-disable @typescript-eslint/naming-convention */

"use client"

import {
  Button,
  IconSuccessCircleBulk,
  ModalConfirm,
} from "@kickavenue/ui/components"
import { useSession } from "next-auth/react"
import { useState } from "react"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { useRaffleStore } from "@stores/raffleStore"

import RaffleCarousel from "./components/RaffleCarousel"
import RafflePage from "./components/RafflePage"
import { useSubmitRaffleEntry } from "./hooks/useSubmitRaffleEntry"

const Raffle = () => {
  const { data: session } = useSession()
  const { raffleData, hasTickets, ticketQuantities } = useRaffleStore()
  const [openModalConfirm, setOpenModalConfirm] = useState(false)
  const { submitRaffleEntry } = useSubmitRaffleEntry()

  const handleSubmitRaffle = async () => {
    const raffleItems = raffleData?.Data?.Data?.content || []
    for (const raffle of raffleItems) {
      if (raffle.is_active) {
        await submitRaffleEntry({
          raffle_item_id: raffle.ID,
          user_id: Number(session?.user.id) || 0,
          used_ticket_count: ticketQuantities[raffle.ID] || 0,
          winner: false,
        })
      }
    }
    setOpenModalConfirm(true)
  }
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="mb-8 pb-16">
        <div className="h-[450px]">
          <RaffleCarousel items={raffleData?.Data?.Data?.content || []} />
        </div>
        <RafflePage />
        <div className="shadow-lg fixed inset-x-0 bottom-0 bg-white p-4">
          <Button
            onClick={handleSubmitRaffle}
            className="!w-full"
            size="lg"
            variant="primary"
            disabled={!hasTickets()}
          >
            Submit Raffle
          </Button>
        </div>
      </div>
      <ModalConfirm
        slotAction={
          <Button
            onClick={() => {
              setOpenModalConfirm(false)
            }}
            size="md"
            variant="primary"
          >
            Ok
          </Button>
        }
        subtitle="You have successfully submitted 1 raffle ticket. 
The winner will be contacted through email
on August 1, 2024. Good Luck!"
        title="Raffle Submitted!"
        open={openModalConfirm}
        image={<IconSuccessCircleBulk className="text-success" />}
        className="text-center"
      />
    </AuthRedirectWrapper>
  )
}

export default Raffle
