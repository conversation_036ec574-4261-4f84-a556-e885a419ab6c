import { useQuery } from "@tanstack/react-query"

import { CheckValidCardNumberAPIRepository } from "@infrastructure/repositories/checkValidNumberRepository"

const useCheckValidCardNumber = (cardNumber: string) => {
  const checkValidCardNumber = async () => {
    const checkValidCardNumberApi = new CheckValidCardNumberAPIRepository()
    const result = await checkValidCardNumberApi.getByBinNumber(
      cardNumber.slice(0, 8),
    )

    return result
  }

  return useQuery({
    queryKey: ["check-valid-card-number", cardNumber],
    queryFn: checkValidCardNumber,
    enabled: cardNumber.length === 16,
  })
}

export default useCheckValidCardNumber
