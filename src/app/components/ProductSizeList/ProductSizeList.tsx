import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"

import CenterWrapper from "@shared/CenterWrapper"
import FixedBottomButton from "@shared/FixedBottomButton"
import { useProductStore } from "stores/productStore"
import { TItemListing } from "types/itemListing.type"
import { useMiscStore } from "stores/miscStore"
import { isAllSizeSelected } from "@utils/productDetail"

import ProductImageSlider from "../ProductImageSlider"

import ProductSizeListData from "./ProductSizeListData"
import { processSummariesItem } from "./ProductSizeList.utils"

const ProductSizeList = () => {
  const session = useSession()
  const router = useRouter()
  const {
    detail: product,
    selectedProductType,
    selectedSize,
  } = useProductStore()
  const { setShowFooter } = useMiscStore()
  const [disabledBtn, setDisabledBtn] = useState(false)
  const summaries = (
    product?.itemPriceSummary || ([] as TItemListing[])
  )?.slice(1)
  const itemListing = product?.itemListing || ({} as TItemListing)
  const sizeId = itemListing?.size?.id
  const dashedType = selectedProductType?.toLowerCase()?.replace(/_/g, "-")

  const list = [] as TItemListing[]
  summaries.forEach((item) =>
    processSummariesItem(item, selectedProductType, list),
  )

  const onHandleContinue = () => {
    if (!session?.data?.user) {
      router.push("/login")
      return
    }
    if (!product.itemListing?.lowestAsk) {
      router.push(`/checkout-preview/${product.id}?key=false`)
      return
    }
    router.push(`/buying/${product.id}/${sizeId}/${dashedType}`)
    setDisabledBtn(true)
  }

  useEffect(() => {
    setShowFooter(false)
    return () => {
      setShowFooter(true)
    }
  }, [setShowFooter])

  return (
    <>
      <CenterWrapper className="!pb-[50px] md:!pb-[200px]">
        <div className="col-span-4 md:col-span-6">
          <ProductImageSlider />
        </div>
        <div className="col-span-4 md:col-span-6">
          <ProductSizeListData list={list} />
        </div>
      </CenterWrapper>
      <FixedBottomButton
        disabled={
          !selectedSize || disabledBtn || isAllSizeSelected(selectedSize)
        }
        onClick={onHandleContinue}
        text="Continue to Buy / Offer"
      />
    </>
  )
}

export default ProductSizeList
