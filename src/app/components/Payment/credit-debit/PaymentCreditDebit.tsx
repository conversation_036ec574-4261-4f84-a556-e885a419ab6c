import NavigationOption from "@kickavenue/ui/dist/src/components/NavigationOption"
import React from "react"

import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import { usePaymentCardFormStore } from "stores/paymentCardFormStore"

import PaymentCreditDebitModal from "./PaymentCreditDebitModal"
import PaymentCardTypeIcon from "./PaymentCardTypeIcon"

const { CREDIT_DEBIT_CARD } = ModalConstant.MODAL_IDS

export default function PaymentCreditDebit() {
  const { setOpen } = useModalStore()
  const { form, binData } = usePaymentCardFormStore()

  return (
    <div className="rounded-base">
      <NavigationOption
        title={String(form?.cardNumber) || "Credit/Debit Card"}
        weight="medium"
        withArrowRight
        iconLeading={<PaymentCardTypeIcon binBrand={binData?.brand} />}
        className="cursor-pointer rounded-base border border-gray-w-80 text-gray-b-40"
        onClick={() => {
          setOpen(true, CREDIT_DEBIT_CARD)
        }}
        disableHover
      />

      <PaymentCreditDebitModal />
    </div>
  )
}
