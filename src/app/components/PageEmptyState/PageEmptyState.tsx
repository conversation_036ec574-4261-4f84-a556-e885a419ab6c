"use client"

import { But<PERSON>, Space } from "@kickavenue/ui/components"
import Empty from "@kickavenue/ui/components/Empty"

interface PageEmptyStateProps {
  title: string
  description: string
  buttonText?: string
  onButtonClick?: () => void
}

const PageEmptyState = ({
  title,
  description,
  buttonText,
  onButtonClick,
}: PageEmptyStateProps) => {
  return (
    <div className="flex h-[550px] flex-col items-center justify-center">
      <Empty
        className="[&>img]:size-[205px]"
        title={title}
        subText={description}
      />
      {buttonText && onButtonClick && (
        <>
          <Space size="lg" type="margin" direction="y" />
          <Button size="md" variant="primary" onClick={onButtonClick}>
            {buttonText}
          </Button>
        </>
      )}
    </div>
  )
}

export default PageEmptyState
