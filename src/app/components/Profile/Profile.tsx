"use client"

import TabSwitcher from "../shared/TabSwitcher/TabSwitcher"

import BankAccount from "./bank-account/BankAccount"
import PersonalInformation from "./personal-information/PersonalInformation"
import SavedAddresses from "./saved-addresses/SavedAddresses"
import UserLocation from "./user-location/UserLocation"
import UserPreferences from "./user-preferences/UserPreferences"

const Profile = () => {
  const profileTabItems = [
    {
      id: "personal-information",
      title: "Personal Information",
      content: <PersonalInformation />,
    },
    {
      id: "saved-addresses",
      title: "Saved Addresses",
      content: <SavedAddresses />,
    },
    {
      id: "bank-account",
      title: "Bank Account",
      content: <BankAccount />,
    },
    {
      id: "user-location",
      title: "User Location",
      content: <UserLocation />,
    },
    {
      id: "user-preferences",
      title: "User Preferences",
      content: <UserPreferences />,
    },
  ]

  return (
    <div className="relative flex size-full flex-col p-sm lg:p-lg">
      <h4 className="text-heading-4 font-bold text-gray-b-75">Profile</h4>
      <TabSwitcher tabs={profileTabItems} defaultTabId="personal-information" />
    </div>
  )
}

export default Profile
