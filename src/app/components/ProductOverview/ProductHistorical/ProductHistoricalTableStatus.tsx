import { TableCell, TableRow, Text } from "@kickavenue/ui/dist/src/components"

import { TProductHistorical } from "types/transactionDetail.type"

export interface TProductHistoricalTableStatusProps {
  data: TProductHistorical | undefined
  isLoading: boolean
}

const ProductHistoricalTableStatus = ({
  data,
  isLoading,
}: TProductHistoricalTableStatusProps) => {
  if (isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={3}>
          <Text
            size="sm"
            type="regular"
            state="secondary"
            className="text-center"
          >
            ...
          </Text>
        </TableCell>
      </TableRow>
    )
  }

  if (!data?.sales?.length) {
    return (
      <TableRow>
        <TableCell colSpan={3}>
          <Text
            size="sm"
            type="regular"
            state="secondary"
            className="text-center"
          >
            No data available
          </Text>
        </TableCell>
      </TableRow>
    )
  }

  return null
}

export default ProductHistoricalTableStatus
