import { useState } from "react"

import { useRegistrationStore } from "@stores/registrationStore"

import {
  registerUser,
  validateOtpLength,
  verifyOtpCodes,
} from "../utils/otpVerificationUtils"

export const useOtpVerification = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [smsError, setSmsError] = useState("")
  const [emailError, setEmailError] = useState("")
  const { registrationData, resetRegistrationData } = useRegistrationStore()

  const handleVerify = async (emailOtp: string, phoneOtp: string) => {
    // Validate OTP length
    const lengthValidation = validateOtpLength(emailOtp, phoneOtp)
    if (!lengthValidation.success) {
      setSmsError(lengthValidation.smsError || "")
      setEmailError(lengthValidation.emailError || "")
      return { success: false }
    }

    setIsLoading(true)
    setSmsError("")
    setEmailError("")

    try {
      // Verify OTP codes
      const verificationResult = await verifyOtpCodes(emailOtp, phoneOtp)
      if (!verificationResult.success) {
        if (verificationResult.error) {
          setSmsError(verificationResult.error)
          setEmailError(verificationResult.error)
        } else {
          setSmsError(verificationResult.smsError || "")
          setEmailError(verificationResult.emailError || "")
        }
        return { success: false }
      }

      // Register user
      const registrationResult = await registerUser(
        registrationData,
        verificationResult.tokenActivation || "",
      )

      if (registrationResult.success) {
        // Clear all registration data
        resetRegistrationData()
        return { success: true }
      }

      setSmsError(registrationResult.error || "Registration failed")
      setEmailError(registrationResult.error || "Registration failed")
      return { success: false }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An error occurred during verification"
      setSmsError(errorMessage)
      setEmailError(errorMessage)
      return { success: false }
    } finally {
      setIsLoading(false)
    }
  }

  return { isLoading, smsError, emailError, handleVerify }
}
