import { ReactNode } from "react"

import Wallet from "@app/components/Profile/Wallet"
import { SidebarProfile } from "@app/components/Profile/SidebarProfile"
import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"

const ProfileLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="bg-gray-w-95 lg:flex lg:justify-center">
        <div className="grid w-full grid-cols-10 gap-lg lg:p-xl xl:max-w-[calc(100vw-80px)] xl:px-0">
          <div className="col-span-10 flex h-full flex-col gap-base lg:col-span-3 xl:col-span-2">
            <Wallet />
            <SidebarProfile />
          </div>
          <div className="col-span-10 m-sm rounded-base bg-white lg:col-span-7 lg:m-0 lg:h-full xl:col-span-8">
            {children}
          </div>
        </div>
      </div>
    </AuthRedirectWrapper>
  )
}

export default ProfileLayout
