import { ComboBox } from "@kickavenue/ui/components"
import { TItemOption } from "@kickavenue/ui/dist/src/components/ComboBox/ComboBox.type"
import React, { useCallback, useMemo } from "react"
import Image from "next/image"

import { TCountry } from "types/country.type"
import { convertS3UrlToCloudFront } from "@utils/misc"

const prettierCountryFlagItem = (c: TCountry) => {
  return {
    label: c.name,
    value: String(c.country),
    icon: c.flag ? (
      <Image
        height={16}
        width={22}
        src={convertS3UrlToCloudFront(c.flag)}
        alt={c.name || ""}
      />
    ) : null,
  } as TItemOption
}

interface InputCountryProps {
  countries: TCountry[]
  selectedCountry: TCountry | null
  setSelectedCountry: (country: TCountry | null) => void
  onSearch?: (query: string) => void
}

const InputCountry: React.FC<InputCountryProps> = ({
  countries,
  selectedCountry,
  setSelectedCountry,
  onSearch,
}) => {
  const countryItems = useMemo(
    () => countries.map((country) => prettierCountryFlagItem(country)),
    [countries],
  )

  const selectedValue = useMemo(
    () => (selectedCountry ? prettierCountryFlagItem(selectedCountry) : null),
    [selectedCountry],
  )

  const handleSelect = useCallback(
    (val: { label: string; value: string } | null) => {
      const selected = val
        ? countries.find((country) => String(country.country) === val.value)
        : null
      setSelectedCountry(selected || null)
    },
    [countries, setSelectedCountry],
  )

  return (
    <ComboBox
      selected={selectedValue as TItemOption}
      setSelected={handleSelect}
      iconLeading={selectedValue?.icon}
      items={countryItems as TItemOption[]}
      placeholder="Search country"
      onChangeSearch={onSearch}
      className="w-full"
    />
  )
}

export default InputCountry
