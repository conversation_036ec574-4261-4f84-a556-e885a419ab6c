import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

import PendingStatus from "./CompletePayment/PendingStatus"
import ProductDetails from "./CompletePayment/ProductDetails"
import PaymentDetails from "./CompletePayment/PaymentDetails"
import DeliveryDetails from "./CompletePayment/DeliveryDetails"
import ShipmentInfo from "./CompletePayment/ShipmentInfo"

const { HISTORY_DETAIL } = ModalConstant.MODAL_IDS

const HistoryDetailModal = () => {
  const { setOpen } = useModalStore()

  const handleClose = () => {
    setOpen(false, HISTORY_DETAIL)
  }

  return (
    <Modal modalId={HISTORY_DETAIL}>
      <div className="flex flex-col gap-4">
        <HeaderModal onClose={handleClose} title="History Detail" />
        <div className="max-h-[450px] overflow-y-auto p-6">
          <PendingStatus />
          <ProductDetails />
          <PaymentDetails />
          <ShipmentInfo />
          <DeliveryDetails />
        </div>
      </div>
    </Modal>
  )
}

export default HistoryDetailModal
