import { Divider, Space, Text } from "@kickavenue/ui/dist/src/components"
import { useCallback } from "react"
import { Controller, FormProvider } from "react-hook-form"

import useFetchSellerStockById from "@app/hooks/useFetchSellerStockById"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import ComboBox from "@components/shared/Form/ComboBox"
import Input from "@components/shared/Form/Input"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { FormFieldConstant } from "@constants/formField"
import { ModalConstant } from "@constants/modal"
import { TransactionConstant } from "@constants/transaction.constant"
import { getListingCondition } from "@utils/selling"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { useModalStore } from "stores/modalStore"
import { EButtonType } from "types/misc.type"
import {
  EConsignmentStatusTrack,
  TConsignmentInputAWBForm,
} from "types/sellerConsignment.type"

import ConsignmentRevenue from "../ConsignmentRevenue"

import ConsignmentInputAWBModalTimeInfo from "./ConsignmentInputAWBModalTimeInfo"
import useEnterAWBConsignment from "./hook/useEnterAWBConsignment"

const { COURIER, AWB } = FormFieldConstant.CONSIGNMENT_INPUT_AWB
const { COURIER_OPTIONS } = TransactionConstant
const { CONSIGNMENT_INPUT_AWB } = ModalConstant.MODAL_IDS

const ConsignmentInputAWBModalContent = () => {
  const { form, onFormValid, isPending } = useEnterAWBConsignment()
  const { sellerStock: selectedSellerStock } = useConsignmentModalStore()
  const { setOpen } = useModalStore()

  const { data: sellerStock, isLoading } = useFetchSellerStockById({
    id: selectedSellerStock?.sellerListingStock?.id,
  })

  const getSelectedCourier = useCallback((value: string | null) => {
    return COURIER_OPTIONS.find((opt) => opt.value === value) || null
  }, [])

  const isSellerDropOff =
    form.watch(COURIER.NAME as keyof TConsignmentInputAWBForm) === "DROPOFF"

  const awbFields = {
    label: isSellerDropOff ? AWB.LABEL_2 : AWB.LABEL,
    placeholder: isSellerDropOff ? AWB.PLACEHOLDER_2 : AWB.PLACEHOLDER,
  }

  if (isLoading) {
    return <SpinnerLoading className="h-[496px] min-w-[350px]" />
  }

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit((data) => onFormValid(data))}>
        <div className="flex h-[496px] max-h-[496px] flex-col gap-lg overflow-y-auto px-lg">
          <ConsignmentInputAWBModalTimeInfo
            sellerStock={sellerStock}
            status={EConsignmentStatusTrack.ConsignmentApproved}
          />
          <Divider orientation="horizontal" />
          <Controller
            control={form.control}
            name={COURIER.NAME as keyof TConsignmentInputAWBForm}
            rules={{ required: true }}
            render={({ field: { onChange, value, ...rest } }) => (
              <ComboBox
                label={COURIER.LABEL}
                state="required"
                placeholder={COURIER.PLACEHOLDER}
                items={COURIER_OPTIONS}
                selected={getSelectedCourier(value)}
                setSelected={(opt) => onChange(opt?.value)}
                {...rest}
              />
            )}
          />
          <Controller
            name={AWB.NAME as keyof TConsignmentInputAWBForm}
            control={form.control}
            rules={{ required: true }}
            render={({ field }) => (
              <Input
                label={awbFields.label}
                placeholder={awbFields.placeholder}
                state="required"
                {...field}
                value={String(field.value || "")}
              />
            )}
          />
          <Divider orientation="horizontal" />
          <div className="flex flex-col gap-sm">
            <Text size="sm" type="bold" state="primary">
              Product Detail
            </Text>
            <div className="flex w-full justify-between rounded-sm border border-solid border-gray-w-80 p-base">
              <ProductDetailPreview
                listing={sellerStock?.sellerListingStock}
                listingCondition={getListingCondition(
                  sellerStock?.sellerListingStock,
                )}
              />
            </div>
          </div>
          <ConsignmentRevenue sellerStock={sellerStock} />
        </div>
        <Space size="lg" direction="y" type="margin" />
        <ModalCancelConfirm
          onCancel={() => setOpen(false, CONSIGNMENT_INPUT_AWB)}
          onConfirm={() => {}}
          confirmBtnType={EButtonType.Submit}
          disableConfirm={isLoading || isPending}
        />
      </form>
    </FormProvider>
  )
}

export default ConsignmentInputAWBModalContent
