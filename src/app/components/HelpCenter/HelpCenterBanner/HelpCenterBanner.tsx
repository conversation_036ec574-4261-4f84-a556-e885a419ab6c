import { Heading } from "@kickavenue/ui/dist/src/components"
import Image from "next/image"

import HelpCenterSection from "../HelpCenterSection"

import HelpCenterBannerSearch from "./HelpCenterBannerSearch"

const HelpCenterBanner = () => {
  return (
    <div className="relative h-[400px] w-full overflow-hidden">
      {/* Background Image */}
      <Image
        src="/help-center-banner.png"
        alt="Help Center Banner"
        fill
        className="object-cover brightness-50"
        priority
      />

      {/* Content Overlay */}
      <HelpCenterSection
        parentClassName="absolute inset-0"
        childClassName="flex flex-col justify-center px-base xl:px-0"
      >
        <Heading heading="4" textStyle="medium" className="mb-xxs text-white">
          Welcome to
        </Heading>
        <Heading heading="4" textStyle="bold" className="mb-lg text-white">
          Kick Avenue Help Center
        </Heading>
        <HelpCenterBannerSearch />
      </HelpCenterSection>
    </div>
  )
}

export default HelpCenterBanner
