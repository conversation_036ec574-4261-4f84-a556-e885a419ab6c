import { useBuyingPendingStore } from "stores/buyingPendingStore"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { isEmpty, isEmptyArray } from "@utils/misc"
import SellingTableEmpty from "@components/SellingDashboard/SellingTableEmpty"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import Table from "@components/shared/Table"
import { TTransactionDetailFilter } from "types/transactionDetail.type"
import TablePagination from "@components/shared/Table/TablePagination"

import useBuyingPendingTable from "./hooks/useBuyingPendingTable"
import useFetchMyPendingBuying from "./hooks/useFetchMyPendingBuying"
import styles from "./BuyingDashboardPendingTable.module.scss"
import useBuyingPendingPagination from "./hooks/useBuyingPendingPagination"

const BuyingDashboardPendingTable = () => {
  const { list, filter, setList, setFilter } = useBuyingPendingStore()

  const { columns } = useBuyingPendingTable()

  const paging = useBuyingPendingPagination()

  const { isLoading } = useFetchMyPendingBuying({
    onSuccess(data) {
      setList(data?.content)
      setFilter({
        ...((filter || {}) as TTransactionDetailFilter),
        totalPages: data?.totalPages,
      })
    },
    filter: filter as TTransactionDetailFilter,
    enabled:
      !isEmpty(filter?.page) &&
      !isEmpty(filter?.pageSize) &&
      !isEmptyArray(filter?.status),
  })

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(100vh-400px)]" />
  }

  if (!isLoading && isEmptyArray(list)) {
    return (
      <SellingTableEmpty
        title="Stay on Top of Your Purchases!"
        subText="You have no pending payments. Complete your purchase to bring home the items you've selected."
        pageLink={PageRouteConstant.SEARCH}
        btnText="Shop New Arrivals"
      />
    )
  }

  return (
    <>
      <div className={styles["table-container"]}>
        <Table
          rowKey="transactionId"
          columns={columns}
          dataSource={list}
          className={styles.table}
        />
      </div>

      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default BuyingDashboardPendingTable
