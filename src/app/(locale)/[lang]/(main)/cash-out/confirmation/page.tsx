"use client"

import { Divider } from "@kickavenue/ui/components"
import { useState } from "react"
import { FormProvider } from "react-hook-form"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import CashOutBalanceInfo from "@components/CashOut/Confirmation/CashOutBalanceInfo"
import CashOutDetails from "@components/CashOut/Confirmation/CashOutDetails"
import CashOutSummary from "@components/CashOut/Confirmation/CashOutSummary"
import CashOutTo from "@components/CashOut/Confirmation/CashOutTo"
import BankSelectionModal from "@components/CashOut/Confirmation/BankSelectionModal"
import KickCreditInfoModal from "@components/KickCredit/components/KickCreditInfoModal"
import useCashOutForm from "@components/CashOut/Confirmation/hooks/useCashOutForm"
import PasswordConfirmation from "@components/CashOut/Confirmation/PasswordConfirmation"
import SellerCreditInfoModal from "@components/SellerCredit/components/SellerCreditInfoModal"
import CashOutFooter from "@components/CashOut/Confirmation/CashOutFooter"
import BankAccountFormModal from "@components/Profile/bank-account/form/BankAccountFormModal"

const CashOutConfirmationPage = () => {
  const { form } = useCashOutForm()
  const [showPasswordConfirmation, setShowPasswordConfirmation] =
    useState(false)

  const handleProceedCashOut = () => {
    setShowPasswordConfirmation(true)
  }

  // If showing password confirmation, render that instead of the main content
  if (showPasswordConfirmation) {
    return (
      <FormProvider {...form}>
        <PasswordConfirmation />
      </FormProvider>
    )
  }

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <FormProvider {...form}>
        <div className="w-full p-base text-center text-heading-4 font-bold">
          Cash Out Confirmation
        </div>
        <div className="bg-gray-w-95 py-lg md:flex md:justify-center">
          <div className="rounded-base bg-white md:w-[612px]">
            <div className="flex flex-col gap-lg p-lg">
              <CashOutBalanceInfo />
              <Divider orientation="horizontal" />
              <CashOutDetails />
              <Divider orientation="horizontal" />
              <CashOutTo />
              <Divider orientation="horizontal" />
              <CashOutSummary />
            </div>

            <Divider orientation="horizontal" />

            <CashOutFooter onProceed={handleProceedCashOut} />
          </div>
        </div>

        <BankSelectionModal />
        <KickCreditInfoModal />
        <SellerCreditInfoModal />
        <BankAccountFormModal />
      </FormProvider>
    </AuthRedirectWrapper>
  )
}

export default CashOutConfirmationPage
