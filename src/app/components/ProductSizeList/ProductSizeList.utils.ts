import { ItemConstant } from "@constants/item"
import { getStripAmount } from "@utils/misc"
import { TItemListing } from "types/itemListing.type"
import {
  TItemCondition,
  TItemConditionEnum,
  TListing,
  TListingDetail,
} from "types/listing.type"
import { TStripePrice } from "types/stripe.type"

const { BrandNewUsed, BrandNew } = TItemConditionEnum

export function determineLowestAskPrice(
  min: number,
  selectedProductType: string,
): number | null {
  if (
    min === Infinity &&
    (selectedProductType === BrandNew || selectedProductType === BrandNewUsed)
  ) {
    return 0
  }
  if (min !== Infinity) {
    return getStripAmount(min)
  }
  return null
}

export const updateMinPrice = (min: number, value: TListingDetail): number => {
  const askingPrice = (value?.askingPrice as TStripePrice)?.minUnitVal
  if (askingPrice < min) {
    return askingPrice
  }
  return min
}

export const updateMinPriceSafely = (
  initialMin: number,
  value: TListingDetail | TListingDetail[],
): { min: number; listing: TListingDetail } => {
  let min = initialMin
  let listing: TListingDetail = {} as TListingDetail
  if (Array.isArray(value)) {
    value.forEach((listingDetail) => {
      min = updateMinPrice(min, listingDetail)
      listing = listingDetail
    })
  } else {
    min = updateMinPrice(min, value)
    listing = value
  }
  return { min, listing }
}

export const processListings = (
  listings: TListing,
  selectedProductType: TItemCondition,
): { min: number; listing: TListingDetail } => {
  let min = Infinity
  let listing: TListingDetail = {} as TListingDetail
  for (const [key, value] of Object.entries(listings)) {
    if (ItemConstant.PRODUCT_CONDITIONS[selectedProductType]?.includes(key)) {
      const { min: newMin, listing: newListing } = updateMinPriceSafely(
        min,
        value,
      )
      if (newMin < min) {
        min = newMin
        listing = newListing
      }
    }
  }
  return { min, listing }
}

export const processSummariesItem = (
  item: TItemListing,
  selectedProductType: TItemCondition,
  list: TItemListing[],
) => {
  const { min, listing } = processListings(
    item.listings || ({} as TListing),
    selectedProductType,
  )
  const lowestAsk = determineLowestAskPrice(min, selectedProductType)
  if (lowestAsk !== null) {
    list.push({
      ...item,
      lowestAsk,
      isConsignment: listing?.isConsignment,
      isConsigment: listing?.isConsigment,
    })
  }
}
