/* eslint-disable @typescript-eslint/naming-convention */

import { useEffect, useState } from "react"

import { UseOtpTimerResult } from "./types"

const handleResendRequestLogic = async (to: string, type: string) => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/otp/resend`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ to, type }),
    },
  )

  if (!response.ok) {
    throw new Error("Failed to resend OTP")
  }
}

export const useOtpTimer = (): UseOtpTimerResult => {
  const [remainingTime, setRemainingTime] = useState(180)
  const [canResend, setCanResend] = useState(false)
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null)

  const startCountdown = () => {
    if (intervalId) {
      clearInterval(intervalId)
    }

    const id = setInterval(() => {
      setRemainingTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(id)
          setCanResend(true)
          return 0
        }
        return prevTime - 1
      })
    }, 1000)
    setIntervalId(id)
  }

  const handleResendRequest = async (
    to: string,
    type: string,
    onError: (error: string) => void,
  ) => {
    if (!canResend) {
      onError(
        `Please wait ${remainingTime} seconds before requesting a new code`,
      )
      return
    }

    try {
      await handleResendRequestLogic(to, type)
      setRemainingTime(180)
      setCanResend(false)
      startCountdown()
    } catch (err) {
      onError(
        err instanceof Error
          ? err.message
          : "An error occurred while resending OTP",
      )
    }
  }

  useEffect(() => {
    setRemainingTime(180)
    setCanResend(false)

    startCountdown()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return { remainingTime, startCountdown, handleResendRequest, canResend }
}
