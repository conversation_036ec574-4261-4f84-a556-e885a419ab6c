/* eslint-disable react-hooks/exhaustive-deps */

"use client"

import { useEffect, useCallback, useState } from "react"
import { useSearchParams } from "next/navigation"
import { debounce } from "lodash"

import useGetMyDisbursements from "@app/hooks/useGetMyDisbursements"
import { updateUrlSearchParams } from "@utils/url.utils"
import {
  EKickCreditDisbursementType,
  EKickPointDisbursementType,
  ESellerCreditDisbursementType,
  GetMyDisbursementParams,
} from "types/disbursement.type"
import Table from "@components/shared/Table"
import TablePagination from "@components/shared/Table/TablePagination"

import PageEmptyState from "../PageEmptyState/PageEmptyState"

import DisbursementDetailModal from "./DisbursementDetailModal/DisbursementDetailModal"
import DisbursementSearchInput from "./DisbursementSearchInput"
import {
  defaultDisbursementFilter,
  prettierDisbursementTable,
} from "./utils/table"
import { disbursementTableHeaders } from "./TableHeaders"
import useDisbursementPagination from "./hooks/useDisbursementPagination"

interface DisbursementDataTableProps {
  disbursementType?:
    | EKickCreditDisbursementType
    | EKickPointDisbursementType
    | ESellerCreditDisbursementType
  emptyStateTitle?: string
  emptyStateDescription?: string
  searchPlaceholder?: string
}

const DisbursementDataTable = ({
  disbursementType = EKickCreditDisbursementType.KickPendingTopUp,
  emptyStateTitle = "No Pending Top Up",
  emptyStateDescription = "It seems you haven't got anything",
  searchPlaceholder,
}: DisbursementDataTableProps) => {
  const searchParams = useSearchParams()
  const [search, setSearch] = useState(searchParams.get("search") || "")
  const [filter, setFilter] = useState<GetMyDisbursementParams>({
    ...defaultDisbursementFilter(),
    pageSize: Number(searchParams.get("pageSize")) || 10,
    search: searchParams.get("search") || "",
  })

  const { data, isLoading } = useGetMyDisbursements(disbursementType, filter)

  const paging = useDisbursementPagination()

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setFilter((prev) => ({ ...prev, search: value }))
      updateUrlSearchParams("search", value)
    }, 500),
    [],
  )

  const handleSearch = useCallback(
    (value: string) => {
      setSearch(value)
      debouncedSearch(value)
    },
    [debouncedSearch],
  )

  useEffect(() => {
    const searchValue = searchParams.get("search")
    if (searchValue !== search) {
      setSearch(searchValue || "")
      setFilter((prev) => ({ ...prev, search: searchValue || "" }))
    }

    return () => {
      debouncedSearch.cancel()
    }
  }, [searchParams])

  if (data && data?.content?.length < 1 && !search) {
    return (
      <PageEmptyState
        title={emptyStateTitle}
        description={emptyStateDescription}
      />
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <DisbursementSearchInput
        value={search}
        onChange={handleSearch}
        placeholder={searchPlaceholder}
      />
      <div className="flex flex-col">
        <div className="overflow-y-auto pb-10">
          <Table
            columns={disbursementTableHeaders(disbursementType)}
            dataSource={prettierDisbursementTable(data?.content)}
            rowKey="id"
            loading={isLoading}
          />
        </div>
        {(data?.totalPages || 0) > 0 && (
          <div className="flex justify-end gap-base">
            <TablePagination {...paging} />
          </div>
        )}
      </div>

      <DisbursementDetailModal />
    </div>
  )
}

export default DisbursementDataTable
