import { IconCloseOutline, Space } from "@kickavenue/ui/components"

import Drawer from "@shared/Drawer"
import { useMiscStore } from "stores/miscStore"

import FilterContent from "./FilterContent"

const FilterDrawer = () => {
  const { showFilterDrawer, setShowFilterDrawer } = useMiscStore()
  return (
    <Drawer
      placement="right"
      open={showFilterDrawer}
      onClose={() => setShowFilterDrawer(false)}
      className="h-full md:hidden"
    >
      <div className="p-base">
        <div className="flex justify-end">
          <IconCloseOutline
            width={24}
            height={24}
            onClick={() => setShowFilterDrawer(false)}
          />
        </div>
        <Space size="base" direction="y" type="margin" />
        <div className="pt-lg">
          <FilterContent />
        </div>
      </div>
    </Drawer>
  )
}

export default FilterDrawer
