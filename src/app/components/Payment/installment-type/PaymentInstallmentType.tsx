import React from "react"
import { RadioButton } from "@kickavenue/ui/components"

import { paymentInstallmentTypeItems } from "@components/Payment/payment.utils"
import ListItem from "@components/shared/ListItem"

export interface PaymentInstallmentTypeProps {
  setInstallmentType: (value: number) => void
}

export default function PaymentInstallmentType(
  props: PaymentInstallmentTypeProps,
) {
  const { setInstallmentType } = props
  return (
    <>
      {paymentInstallmentTypeItems.map((item) => {
        const { title, subtitle, description, titleProps, value } = item
        return (
          <div className="rounded-base border border-gray-w-80" key={title}>
            <ListItem
              title={title}
              titleProps={titleProps}
              withDivider={false}
              trailingIcon={
                <RadioButton
                  name="installment-type"
                  value={value}
                  className="[&>div]:!hidden"
                  onChange={() => setInstallmentType(value)}
                />
              }
              subtitle={subtitle}
              description={description}
              classes={{
                wrapper: "p-sm",
                content: "!ml-0 gap-y-xxs",
              }}
            />
          </div>
        )
      })}
    </>
  )
}
