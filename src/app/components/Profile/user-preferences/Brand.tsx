"use client"

import { <PERSON><PERSON>, <PERSON>B<PERSON> } from "@kickavenue/ui/components"
import React from "react"
import { useRouter } from "next/navigation"

import SearchInput from "@components/shared/SearchInput"
import Spinner from "@components/shared/Spinner/Spinner"

import useBrandPreference from "./hooks/useBrandPreference"

const Brand = ({ handleNext }: { handleNext: () => void }) => {
  const router = useRouter()

  const {
    data,
    handleCheckboxChange,
    saveMutation,
    search,
    handleSearch,
    isLoading,
    selectedBrands,
  } = useBrandPreference({
    onSuccessSave: () => {
      handleNext()
      router.refresh()
    },
  })

  return (
    <>
      <div className="p-lg pb-sm">
        <SearchInput
          value={search}
          onChange={(e) => handleSearch(e.currentTarget.value)}
          onClearText={() => handleSearch("")}
        />
      </div>
      <div className="h-[300px] overflow-y-scroll">
        {isLoading && (
          <div className="flex h-full items-center justify-center">
            <Spinner />
          </div>
        )}
        {!isLoading && (
          <div className="flex flex-col gap-y-base p-lg">
            {data?.map((item) => (
              <CheckBox
                onChange={() => handleCheckboxChange(item)}
                key={item.id}
                label={item.name}
                checked={selectedBrands.some((brand) => brand.id === item.id)}
              />
            ))}
          </div>
        )}
      </div>
      <div className="p-lg shadow-base">
        <Button
          onClick={() => {
            saveMutation.mutate()
          }}
          size="lg"
          variant="primary"
          className="!w-full"
          disabled={selectedBrands?.length === 0 || saveMutation.isPending}
        >
          Next
        </Button>
      </div>
    </>
  )
}

export default Brand
