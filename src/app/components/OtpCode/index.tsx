"use client"

import React, { useEffect } from "react"

import { useRegistrationStore } from "@stores/registrationStore"

import useOTP from "./hooks/useOTP"
import { useOtpTimer } from "./hooks/useOtpTimer"
import OTPInputField from "./components/OTPInputField"
import ErrorMessage from "./components/ErrorMessage"
import ResendOTP from "./components/ResendOTP"

interface OTPInputProps {
  onChange: (value: string) => void
  type: "EMAIL" | "SMS"
  error: string
}

const OTPInput: React.FC<OTPInputProps> = ({ onChange, type, error }) => {
  const { registrationData } = useRegistrationStore()
  const { otpValues, setError, handleInputChange, handleKeyDown, handlePaste } =
    useOTP(type?.toLowerCase() as "phone" | "email")
  const { remainingTime, handleResendRequest } = useOtpTimer()

  useEffect(() => {
    const combinedOtp = otpValues.join("")
    onChange(combinedOtp)
  }, [otpValues, onChange])

  useEffect(() => {
    if (error) {
      setError(error)
    }
  }, [error, setError])

  const onResendRequest = async () => {
    const to =
      type === "EMAIL" ? registrationData.email : registrationData.phoneNumber
    await handleResendRequest(to, type, setError)
  }

  return (
    <div className="bg-gray-100 flex flex-col items-center justify-center">
      <div className="shadow-md rounded-lg bg-white px-6">
        <OTPInputField
          otpValues={otpValues}
          error={error}
          handleInputChange={handleInputChange}
          handleKeyDown={handleKeyDown}
          handlePaste={handlePaste}
          type={type}
        />
        <ErrorMessage error={error} />
        <ResendOTP
          remainingTime={remainingTime}
          handleResendRequest={onResendRequest}
        />
      </div>
    </div>
  )
}

export default OTPInput
