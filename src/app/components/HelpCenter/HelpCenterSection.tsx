import { cx } from "class-variance-authority"

interface HelpCenterSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  parentClassName?: string
  childClassName?: string
}

const HelpCenterSection = ({
  children,
  parentClassName,
  childClassName,
  ...rest
}: HelpCenterSectionProps) => {
  return (
    <div className={cx("flex justify-center", parentClassName)} {...rest}>
      <div className={cx("w-full max-w-[1440px]", childClassName)}>
        {children}
      </div>
    </div>
  )
}

export default HelpCenterSection
