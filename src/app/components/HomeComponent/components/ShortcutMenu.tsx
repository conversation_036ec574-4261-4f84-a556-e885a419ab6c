import React from "react"

import Shortcut from "@app/components/Shortcut"

const shortcutData = [
  { id: "editors-pick", text: "Editor's Pick" },
  { id: "top-rated", text: "Top Rated" },
  { id: "new-arrivals", text: "New Arrivals" },
  { id: "best-sellers", text: "Best Sellers" },
  { id: "on-sale", text: "On Sale" },
  { id: "trending", text: "Trending" },
  { id: "featured", text: "Featured" },
  { id: "most-popular", text: "Most Popular" },
]

const ShortcutMenu: React.FC = () => {
  return (
    <div className="mx-auto w-full max-w-[1440px] px-6 py-base sm:px-12 sm:py-lg md:px-24 md:py-xl">
      <div className="grid gap-lg md:grid-cols-4">
        {shortcutData.map((item) => (
          <Shortcut
            key={item.id}
            imageSrc="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            text={item.text}
          />
        ))}
      </div>
    </div>
  )
}

export default ShortcutMenu
