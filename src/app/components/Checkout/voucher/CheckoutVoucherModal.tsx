import React, { useEffect, useMemo, useState } from "react"
import { But<PERSON> } from "@kickavenue/ui/components"

import Modal from "@components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { TVoucher } from "types/voucher.type"
import { useCheckoutStore } from "stores/checkoutStore"
import HeaderModal from "@components/shared/HeaderModal"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import { ModalConstant } from "@constants/modal"
import { usePaymentStore } from "stores/paymentStore"

import CheckoutVoucherModalContent from "./CheckoutVoucherModalContent"

const { CHECKOUT_VOUCHER } = ModalConstant.MODAL_IDS

export default function CheckoutVoucherModal() {
  const { setOpen, open, modalId } = useModalStore()
  const { setVoucher, voucher } = useCheckoutStore()
  const [selectedVoucher, setSelectedVoucher] = useState<TVoucher | undefined>()
  const { resetPaymentState } = usePaymentStore()

  const isModalOpen = open && modalId === CHECKOUT_VOUCHER

  const handleApplyVoucher = () => {
    setOpen(false)
    resetPaymentState()
    setVoucher(selectedVoucher ? selectedVoucher : undefined)
  }

  useEffect(() => {
    setSelectedVoucher(voucher)
  }, [isModalOpen, voucher])

  const applyText = useMemo(() => {
    if (!selectedVoucher) return "Apply Without Voucher"
    return "Apply"
  }, [selectedVoucher])

  if (!isModalOpen) return null

  return (
    <Modal
      modalId={CHECKOUT_VOUCHER}
      onClose={() => setOpen(false, CHECKOUT_VOUCHER)}
    >
      <HeaderModal
        onClose={() => setOpen(false, CHECKOUT_VOUCHER)}
        title="Voucher"
      />
      <CheckoutVoucherModalContent
        selectedVoucher={selectedVoucher}
        onSelectVoucher={setSelectedVoucher}
      />
      <ModalFooter>
        <div className="col-span-12">
          <Button
            size="lg"
            variant="primary"
            className="!w-full"
            onClick={handleApplyVoucher}
          >
            {applyText}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}
