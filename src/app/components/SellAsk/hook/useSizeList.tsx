import { useCallback, useState } from "react"

import { getSizeIdFromSelectedSize } from "@utils/sellAsk.utils"
import { useProductStore } from "stores/productStore"
import { THighestOffer } from "types/product.type"
import {
  EChipSellType,
  EChipSellState,
} from "@components/shared/Chip/ChipSell.type"

const { Ask, Base } = EChipSellType
const { Default, Hover, Pressed } = EChipSellState

const useSellAskSizeList = () => {
  const { selectedSize, setSelectedSize } = useProductStore()

  const [hoveredSize, setHoveredSize] = useState<Record<string, boolean>>(
    {} as Record<string, boolean>,
  )

  const handleChipClick = useCallback(
    (offer: THighestOffer) => {
      setSelectedSize({ [offer.size.id]: true })
    },
    [setSelectedSize],
  )

  const getChipState = useCallback(
    (offer: THighestOffer) => {
      const sizeId = getSizeIdFromSelectedSize(selectedSize || {})
      if (Number(sizeId) === offer.size.id) {
        return Pressed
      }
      if (hoveredSize?.[offer.size.id]) {
        return Hover
      }
      return Default
    },
    [hoveredSize, selectedSize],
  )

  const getChipType = useCallback((offer: THighestOffer) => {
    return offer?.highestOffer ? Base : Ask
  }, [])

  const handleChipHovered = useCallback(
    (isHovered: boolean, offer: THighestOffer) => {
      setHoveredSize({ [offer.size.id]: isHovered })
    },
    [setHoveredSize],
  )

  return {
    getChipState,
    handleChipHovered,
    getChipType,
    handleChipClick,
  }
}

export default useSellAskSizeList
