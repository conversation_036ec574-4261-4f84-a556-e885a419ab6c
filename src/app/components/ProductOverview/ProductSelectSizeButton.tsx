"use client"

import { But<PERSON> } from "@kickavenue/ui/components/Button"
import Space from "@kickavenue/ui/components/Space"
import Text from "@kickavenue/ui/components/Text"
import { IconArrowDownOutline } from "@kickavenue/ui/components/icons"

import { useProductStore } from "stores/productStore"
import useSelectedSize from "@app/hooks/useSelectedSize"
import ClickableDiv from "@components/shared/ClickableDiv"

import SizeSelectionModal from "./SizeSelectionModal"

const ProductSelectSizeButton = () => {
  const { setShowSizeSelection, showSizeSelection } = useProductStore()
  const renderModal = showSizeSelection ? <SizeSelectionModal /> : null
  const { selectedSize } = useSelectedSize("All")
  return (
    <>
      <ClickableDiv
        keyDownHandler={() => {}}
        onClick={() => setShowSizeSelection(true)}
        className="mb-lg flex items-center justify-between rounded-sm border border-gray-w-80 pl-base"
      >
        <Text size="base" state="primary" type="regular">
          Size
        </Text>
        <Button size="lg" variant="link">
          {selectedSize}
          <Space size="xs" direction="x" type="margin" />
          <IconArrowDownOutline />
        </Button>
      </ClickableDiv>
      {renderModal}
    </>
  )
}

export default ProductSelectSizeButton
