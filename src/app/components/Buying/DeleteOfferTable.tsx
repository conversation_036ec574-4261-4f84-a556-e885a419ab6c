import { formatCurrency } from "@utils/separator"
import { TTableColumn } from "types/table.type"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { TBuyingOffer } from "types/buyingOffer.type"
import BulkActionTable from "@shared/BulkActionTable"
import styles from "@shared/BulkActionTable/BulkActionTable.module.scss"
import { formatDateFull } from "@utils/misc"

const columns: TTableColumn[] = [
  {
    key: "itemName",
    title: "Product Name",
    width: 300,
    headerClassName: styles["sticky-header-1"],
    contentClassName: styles["sticky-content-1"],
    sorter: (a: TBuyingOffer, b: TBuyingOffer) =>
      a?.itemName?.localeCompare(b?.itemName),
    defaultSortOrder: "ascend",
    render: (record: TBuyingOffer) => <>{record?.itemName}</>,
  },
  {
    key: "sku",
    title: "Sku",
    width: 50,
    sorter: (a: TBuyingOffer, b: TBuyingOffer) =>
      (a.itemSku || "").localeCompare(b.itemSku || ""),
    render: (record: TBuyingOffer) => <>{record.itemSku}</>,
  },
  {
    key: "size",
    title: "Size",
    width: 50,
    sorter: (a: TBuyingOffer, b: TBuyingOffer) =>
      (a.size?.us || "").localeCompare(b.size?.us || ""),
    render: (record: TBuyingOffer) => <>{`US ${record.size.us}`}</>,
  },
  {
    key: "amount",
    title: "Offer Price",
    width: 50,
    headerClassName: "[&>div]:justify-end",
    contentClassName: "text-right",
    render: (record: TBuyingOffer) => (
      <>{formatCurrency(record?.amount / 100 || 0, ",", "IDR")}</>
    ),
    sorter: (a: TBuyingOffer, b: TBuyingOffer) =>
      (a?.amount || 0) - (b?.amount || 0),
  },
  {
    key: "expiredAt",
    title: "Expiry Date",
    headerClassName: "[&>div]:justify-end",
    contentClassName: "text-right",
    width: 100,
    sorter: (a: TBuyingOffer, b: TBuyingOffer) =>
      (a?.expiredAt || "").localeCompare(b?.expiredAt || ""),
    render: (record: TBuyingOffer) => <>{formatDateFull(record?.expiredAt)}</>,
  },
]

const DeleteOfferTable = () => {
  const { selectedRowKeys, buyingOfferData } = useBuyingOfferStore()

  return (
    <BulkActionTable
      columns={columns}
      data={buyingOfferData}
      selectedRowKeys={selectedRowKeys}
    />
  )
}

export default DeleteOfferTable
