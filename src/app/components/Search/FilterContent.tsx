import { But<PERSON>, Di<PERSON><PERSON>, <PERSON> } from "@kickavenue/ui/components"
import { usePathname, useRouter } from "next/navigation"
import { useCallback } from "react"

import FilterGroup from "@components/Filter"
import useFetchCategories from "@app/hooks/useFetchCategories"
import { condition, gender, shippingMethod } from "@utils/filterData"
import useSearchFilter from "@app/hooks/useSearchFilter"
import FilterCategory from "@components/Filter/components/FilterCategory"
import FilterBrand from "@components/Filter/components/FilterBrand"
import FilterSubcategoryCollection from "@components/Filter/components/FilterSubcategoryCollection"
import FilterSize from "@components/Filter/components/FilterSize"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { PARAMS_SEARCH } from "@constants/params.constant"
import { createValueManager } from "@utils/string.utils"
import { useMiscStore } from "stores/miscStore"

const FilterContent = () => {
  const router = useRouter()
  const pathname = usePathname()
  const replaceQueryParams = useReplaceQueryParams()
  const { setSearchKeyword } = useMiscStore()
  const { categories } = useFetchCategories()
  const { isFilterEmpty } = useSearchFilter()

  const isCollectionPage = pathname.includes(PageRouteConstant.COLLECTION)

  const onResetClick = useCallback(() => {
    const uniqueParamsSearch = createValueManager(
      Array.from(new Set(Object.values(PARAMS_SEARCH).flat())),
    ).removeValues(["sortBy"])

    if (isCollectionPage) {
      setSearchKeyword("")
      replaceQueryParams({}, uniqueParamsSearch)
    } else {
      router.push(PageRouteConstant.SEARCH)
    }
  }, [isCollectionPage, replaceQueryParams, router, setSearchKeyword])

  return (
    <>
      <div className="flex justify-between">
        <div className="text-lg font-semibold">Filter</div>
        <Button
          size="lg"
          variant="link"
          className="!p-0 !text-base"
          onClick={onResetClick}
          disabled={isFilterEmpty}
        >
          Reset
        </Button>
      </div>
      <Space type="margin" direction="y" size="base" />
      <Divider type="solid" orientation="horizontal" />
      <FilterCategory cachedCategories={categories} />
      <FilterSubcategoryCollection cachedCategories={categories} />
      <FilterBrand />
      <FilterGroup
        title="Shipping Method"
        type="default"
        items={shippingMethod}
      />
      <FilterGroup title="Gender" type="default" items={gender} />
      <FilterSize />
      <FilterGroup title="Condition" type="default" items={condition} />
      <FilterGroup
        title="Price Range"
        type="priceRange"
        currency="IDR"
        onPriceChange={() => {}}
        className="!mb-0"
      />
    </>
  )
}

export default FilterContent
