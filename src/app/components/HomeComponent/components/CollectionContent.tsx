import React from "react"
import Link from "next/link"
import { Button, Heading, Text } from "@kickavenue/ui/components"

interface CollectionContentProps {
  title: string
  description: string
  redirectionUrl: string
  actionTitle?: string
}

const CollectionContent: React.FC<CollectionContentProps> = ({
  title,
  description,
  redirectionUrl,
  actionTitle,
}) => (
  <div className="flex items-start justify-between md:items-center">
    <div className="flex flex-col gap-xs">
      <Heading heading="4" textStyle="bold">
        {title}
      </Heading>
      <Text type="regular" size="base" state="secondary">
        {description}
      </Text>
    </div>
    <Link href={redirectionUrl}>
      <Button variant="link" className="mt-4 md:mt-0">
        <Text type="bold" size="base" state="success" className="!text-green">
          {actionTitle || "View All"}
        </Text>
      </Button>
    </Link>
  </div>
)

export default CollectionContent
