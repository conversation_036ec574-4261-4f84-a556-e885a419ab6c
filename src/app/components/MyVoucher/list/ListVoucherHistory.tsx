import React from "react"

import { useMyVoucherHistory } from "../hooks/useMyVoucherHistory"

import ListVoucher from "./ListVoucher"

export default function ListVoucherHistory() {
  const {
    items,
    searchVoucher,
    scrollContainerRef,
    isSuccess,
    isLoading,
    isFetchingNextPage,
    searchQuery,
  } = useMyVoucherHistory()
  return (
    <ListVoucher
      type="history"
      items={items}
      isLoading={isLoading}
      isSuccess={isSuccess}
      isFetchingNextPage={isFetchingNextPage}
      scrollContainerRef={scrollContainerRef}
      onModalOpen={() => {}}
      searchVoucher={searchVoucher}
      searchQuery={searchQuery}
    />
  )
}
