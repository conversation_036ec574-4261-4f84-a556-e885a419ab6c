import Divider from "@kickavenue/ui/components/Divider"
import Statistic from "@kickavenue/ui/components/Statistic"

import { formatPrice, getStripAmount } from "@utils/misc"
import { TItemSummary } from "types/itemSummary.type"
import { EOfferType } from "types/offer.type"

const getOfferPriceData = ({
  summary,
  sizeId,
  offerType,
}: {
  summary: TItemSummary | undefined
  sizeId: number
  offerType?: EOfferType
}) => {
  let lowestAsk = 0
  let highestOffer = 0

  if (offerType === EOfferType.Express) {
    lowestAsk =
      getStripAmount(summary?.lowestAsk?.expressBrandNewNoDefect?.[sizeId]) ||
      Infinity
    highestOffer =
      getStripAmount(
        summary?.highestOffer?.expressBrandNewNoDefect?.[sizeId],
      ) || 0
  }

  if (offerType === EOfferType.PreOrder) {
    lowestAsk =
      getStripAmount(summary?.lowestAsk?.preOrderBrandNewNoDefect?.[sizeId]) ||
      Infinity
    highestOffer =
      getStripAmount(
        summary?.highestOffer?.preOrderBrandNewNoDefect?.[sizeId],
      ) || 0
  }

  if (offerType === EOfferType.Standard) {
    lowestAsk =
      getStripAmount(summary?.lowestAsk?.standardBrandNewNoDefect?.[sizeId]) ||
      Infinity
    highestOffer =
      getStripAmount(
        summary?.highestOffer?.standardBrandNewNoDefect?.[sizeId],
      ) || 0
  }

  for (const [, value] of Object.entries(summary?.lowestAsk || {})) {
    const price = getStripAmount(value?.[sizeId])
    if (price > 0 && price < lowestAsk) {
      lowestAsk = price
    }
  }

  for (const [, value] of Object.entries(summary?.highestOffer || {})) {
    const price = getStripAmount(value?.[sizeId])
    if (price > highestOffer) {
      highestOffer = price
    }
  }

  return {
    lowestAsk: lowestAsk === Infinity ? null : lowestAsk,
    highestOffer: highestOffer === 0 ? null : highestOffer,
  }
}

interface BuyingDashboardOfferDetailModalStatisticProps {
  sizeId?: number
  summary?: TItemSummary
  offerType?: EOfferType
}

const BuyingDashboardOfferDetailModalStatistic = ({
  sizeId,
  summary,
  offerType,
}: BuyingDashboardOfferDetailModalStatisticProps) => {
  const { lowestAsk, highestOffer } = getOfferPriceData({
    summary,
    sizeId: sizeId as number,
    offerType,
  })

  return (
    <div className="flex justify-evenly rounded-sm bg-gray-w-95 p-sm">
      <Statistic
        content={formatPrice(lowestAsk || 0, null, "IDR")}
        title="Lowest Ask"
        type="basic"
      />
      <div className="h-[44px]">
        <Divider orientation="vertical" />
      </div>
      <Statistic
        content={formatPrice(highestOffer || 0, null, "IDR")}
        title="Highest Offer"
        type="basic"
      />
    </div>
  )
}

export default BuyingDashboardOfferDetailModalStatistic
