import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divide<PERSON>,
  <PERSON>,
} from "@kickavenue/ui/dist/src/components"
import { Controller } from "react-hook-form"

import useFetchSellerListingById from "@app/hooks/useFetchSellerListingById"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import ProductDetailPreview from "@components/CheckoutPreview/ProductDetailPreview"
import { useConsignmentModalStore } from "stores/consignmentModalStore"
import { formatDateWIB } from "@utils/misc"
import Input from "@components/shared/Form/Input"
import ModalRoundedContent from "@components/shared/ModalParts/ModalRoundedContent"
import ModalRoundedContentItem from "@components/shared/ModalParts/ModalRoundedContentItem"
import ModalFooter from "@components/shared/ModalParts/ModalFooter"
import { TListingStatusEnum } from "types/listingItem.type"
import {
  getListingCondition,
  getSellingStatusMap,
  getUpdatePriceValue,
} from "@utils/selling"
import useSubmitUpdatePrice from "@components/SellingDashboard/SellingDetailModal/hook/useSubmitUpdatePrice"
import SellingDetailModalStatistic from "@components/SellingDashboard/SellingDetailModal/SellingDetailModalStatistic"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"

const ConsignmentActiveDetailModalContent = () => {
  const { sellerListingId } = useConsignmentModalStore()
  const { data: listing, isLoading } = useFetchSellerListingById(
    sellerListingId as number,
  )

  const { form, onFormValid, handleOnPriceChange, disabled } =
    useSubmitUpdatePrice(listing)

  const consignmentStatusMap = getSellingStatusMap(
    listing?.status as TListingStatusEnum,
  )

  if (isLoading) {
    return <SpinnerLoading className="min-w-[350px]" />
  }

  return (
    <>
      <form onSubmit={form.handleSubmit(onFormValid)}>
        <div className="flex flex-col gap-lg p-lg">
          <div className="flex justify-between">
            <Text size="base" type="bold" state="primary">
              Listing Status
            </Text>
            <Badge
              text={consignmentStatusMap.text}
              type={consignmentStatusMap?.badgeType}
            />
          </div>
          <Divider orientation="horizontal" />
          <ProductDetailPreview
            listing={listing}
            listingCondition={getListingCondition(listing)}
          />
          <Divider orientation="horizontal" />
          <SellingDetailModalStatistic
            itemId={listing?.item?.id as number}
            sizeId={listing?.size?.id as number}
          />
          <Controller
            control={form.control}
            name="price"
            rules={{ required: true }}
            render={({ field }) => (
              <Input
                label="Listing Price"
                prefix="IDR"
                state="required"
                {...field}
                value={getUpdatePriceValue(field.value)}
                onChange={(e) => handleOnPriceChange(e, field.onChange)}
              />
            )}
          />
          <ModalRoundedContent title="Listing Detail">
            <div className="flex w-full justify-between px-base">
              <Text size="sm" state="primary" type="regular">
                Consignment ID
              </Text>
              <div className="flex items-center gap-x-xxxs">
                <CodeWithCopyButton
                  code={listing?.consignmentId as string}
                  message="Consignment ID successfully copied!"
                />
              </div>
            </div>
            <ModalRoundedContentItem
              text="Listing Date"
              value={formatDateWIB(listing?.createdAt)}
            />
          </ModalRoundedContent>
        </div>
        <ModalFooter>
          <div className="col-span-12">
            <Button
              size="lg"
              variant="primary"
              disabled={disabled || isLoading}
              className="!w-full"
              type="submit"
            >
              Submit
            </Button>
          </div>
        </ModalFooter>
      </form>
    </>
  )
}

export default ConsignmentActiveDetailModalContent
