import React, { useMemo, useState } from "react"
import { Select } from "@kickavenue/ui/components"

import { event } from "@lib/gtag"

import {
  FilterGroupProps,
  isDefaultFilterGroupProps,
  isSizeFilterGroupProps,
  isPriceRangeFilterGroupProps,
  FilterItem,
  SizeOption,
} from "../types"

import { SizeButton, DefaultItem, ColorItem } from "./FilterItem"
import { PriceInput } from "./PriceInput"

interface FilterContentProps {
  props: FilterGroupProps
  displayedItems: FilterItem[]
  selectedItems: string[]
  handleItemClick: (itemName: string) => void
  currentSizeType: string
  setCurrentSizeType: React.Dispatch<React.SetStateAction<string>>
}

interface PriceRangeFilterProps {
  currency: string
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({ currency }) => {
  const [minPrice, setMinPrice] = useState("")
  const [maxPrice, setMaxPrice] = useState("")

  return (
    <form onSubmit={(e) => e.preventDefault()} className="space-y-2">
      <PriceInput
        prefix={currency}
        placeholder="Minimum Price"
        value={minPrice}
        onChange={setMinPrice}
      />
      <PriceInput
        prefix={currency}
        placeholder="Maximum Price"
        value={maxPrice}
        onChange={setMaxPrice}
      />
    </form>
  )
}

const SizeFilter: React.FC<{
  currentSizeType: string
  setCurrentSizeType: React.Dispatch<React.SetStateAction<string>>
  sizeTypes: string[]
  selectedItems: string[]
  handleItemClick: (itemName: string) => void
  sizeOptions: SizeOption[]
}> = ({
  currentSizeType,
  setCurrentSizeType,
  sizeTypes,
  selectedItems,
  handleItemClick,
  sizeOptions,
}) => (
  <div className="">
    <Select
      value={currentSizeType}
      options={sizeTypes}
      onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
        setCurrentSizeType(e.target.value)
      }
    >
      {sizeTypes.map((type) => (
        <option key={type} value={type}>
          {type}
        </option>
      ))}
    </Select>
    <div className="mt-4 grid grid-cols-4 gap-3">
      {sizeOptions.map((item) => (
        <SizeButton
          key={item.value}
          size={item.label}
          isSelected={selectedItems.includes(item.value)}
          onClick={() => handleItemClick(item.value)}
        />
      ))}
    </div>
  </div>
)

export const FilterContent: React.FC<FilterContentProps> = ({
  props,
  displayedItems,
  selectedItems,
  handleItemClick,
  currentSizeType,
  setCurrentSizeType,
}) => {
  const renderDefaultFilter = useMemo(() => {
    if (isDefaultFilterGroupProps(props)) {
      if (props.type === "color") {
        return (
          <div className="grid grid-cols-4 gap-2">
            {displayedItems.map((item) => (
              <ColorItem
                key={item.id}
                item={item}
                isSelected={selectedItems.includes(item.name)}
                onClick={() => {
                  handleItemClick(item.name)
                  event({
                    action: "product_searched",
                    params: {
                      color: item.name,
                    },
                  })
                }}
              />
            ))}
          </div>
        )
      }
      return (
        <div className="">
          {displayedItems.map((item) => (
            <DefaultItem
              key={item.id}
              item={item}
              isSelected={selectedItems.includes(item.name)}
              onClick={() => {
                event({
                  action: "product_searched",
                  params: {
                    [props.title.replace(/ /g, "_").toLowerCase()]: `${
                      item.name
                    } | ${selectedItems.map((val) => val).join(" | ")}`,
                  },
                })
                handleItemClick(item.name)
              }}
            />
          ))}
        </div>
      )
    }
    return null
  }, [props, displayedItems, selectedItems, handleItemClick])

  if (isPriceRangeFilterGroupProps(props)) {
    return <PriceRangeFilter currency={props.currency} />
  }

  if (isSizeFilterGroupProps(props)) {
    return (
      <SizeFilter
        currentSizeType={currentSizeType}
        setCurrentSizeType={setCurrentSizeType}
        sizeTypes={props.sizeTypes}
        sizeOptions={props.sizeOptions}
        selectedItems={selectedItems}
        handleItemClick={handleItemClick}
      />
    )
  }

  return renderDefaultFilter
}
