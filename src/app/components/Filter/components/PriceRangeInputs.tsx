import React, { useCallback, useState, useEffect } from "react"
import { debounce } from "lodash"

import { PriceInput } from "./PriceInput"

interface PriceRangeFilterProps {
  currency: string
  handlePriceChange: (min: string, max: string) => void
  minPrice: string
  maxPrice: string
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
  currency,
  handlePriceChange,
  minPrice,
  maxPrice,
}) => {
  const [localMinPrice, setLocalMinPrice] = useState(minPrice)
  const [localMaxPrice, setLocalMaxPrice] = useState(maxPrice)

  useEffect(() => {
    setLocalMinPrice(minPrice)
    setLocalMaxPrice(maxPrice)
  }, [minPrice, maxPrice])

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleChange = useCallback(
    debounce((min: string, max: string) => {
      handlePriceChange(min, max)
    }, 500),
    [handlePriceChange],
  )

  useEffect(() => {
    return () => {
      debouncedHandleChange.cancel()
    }
  }, [debouncedHandleChange])

  return (
    <form onSubmit={(e) => e.preventDefault()} className="space-y-2">
      <PriceInput
        prefix={currency}
        placeholder="Minimum Price"
        value={localMinPrice}
        onChange={(value) => {
          setLocalMinPrice(value)
          debouncedHandleChange(value, localMaxPrice)
        }}
      />
      <PriceInput
        prefix={currency}
        placeholder="Maximum Price"
        value={localMaxPrice}
        onChange={(value) => {
          setLocalMaxPrice(value)
          debouncedHandleChange(localMinPrice, value)
        }}
      />
    </form>
  )
}

export default PriceRangeFilter
