import { Divider } from "@kickavenue/ui/components"

const PendingStatus = () => {
  return (
    <>
      <div className="flex justify-between pb-6">
        <div className="text-base font-bold">Pending Status</div>
        <div className="rounded-sm border border-warning-w-70 bg-warning-w-90 px-2 py-1 text-sm font-bold text-warning">
          Waiting for Payment
        </div>
      </div>
      <Divider orientation="horizontal" />
    </>
  )
}

export default PendingStatus
