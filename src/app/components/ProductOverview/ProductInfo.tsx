"use client"

import Heading from "@kickavenue/ui/components/Heading"
import Text from "@kickavenue/ui/components/Text"

import useShowProductTrade from "@app/hooks/useShowProductTrade"
import { useProductStore } from "stores/productStore"
import { TCountry } from "types/country.type"
import { formatPrice } from "@utils/productDetail"
import {
  concatBrand,
  formatRetailPrice,
  isShowUnderRetail,
} from "@utils/product.utils"

import ProductSelectSizeButton from "./ProductSelectSizeButton"

const ProductInfo = () => {
  const { ref } = useShowProductTrade()
  const { detail: product } = useProductStore()
  const { itemListing, country, brands } = product
  const showUnderRetail = isShowUnderRetail(
    product?.retailPrice,
    itemListing?.lowestAsk,
  )
  const formatedRetailPrice = formatRetailPrice(product?.retailPrice)
  return (
    <div ref={ref}>
      <div className="mb-sm flex flex-col gap-xxs">
        <Text size="base" type="regular" state="secondary">
          Lowest Ask
        </Text>
        <Heading heading="4" textStyle="bold">
          {formatPrice(
            itemListing?.lowestAsk || 0,
            country || ({} as TCountry),
          )}
        </Heading>
        {showUnderRetail && (
          <Text
            className="mb-sm"
            size="base"
            type="strikethrough"
            state="disabled"
          >
            {formatedRetailPrice}
          </Text>
        )}
      </div>
      <Text className="mb-xxs" size="base" type="regular" state="secondary">
        {concatBrand(brands)}
      </Text>
      <Heading className="mb-sm" heading="5" textStyle="medium">
        {product?.name || ""}
      </Heading>
      <ProductSelectSizeButton />
    </div>
  )
}

export default ProductInfo
