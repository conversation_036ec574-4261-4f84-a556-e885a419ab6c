import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useBuyingInProgressStore } from "stores/buyingInProgressStore"
import { useModalStore } from "stores/modalStore"

import BuyingDashboardInProgressDetailModalContent from "./BuyingDashboardInProgressDetailModalContent"

const { BUYING_IN_PROGRESS_DETAIL } = ModalConstant.MODAL_IDS

const BuyingDashboardInProgressDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setSelectedRowKeys } = useBuyingInProgressStore()

  const handleClose = () => {
    setOpen(false, BUYING_IN_PROGRESS_DETAIL)
    setSelectedRowKeys([])
  }

  if (!open || modalId !== BUYING_IN_PROGRESS_DETAIL) return null

  return (
    <Modal
      modalId={BUYING_IN_PROGRESS_DETAIL}
      onClose={handleClose}
      className="!min-h-[612px]"
    >
      <HeaderModal onClose={handleClose} title="In Progress Detail" />
      <BuyingDashboardInProgressDetailModalContent />
    </Modal>
  )
}

export default BuyingDashboardInProgressDetailModal
