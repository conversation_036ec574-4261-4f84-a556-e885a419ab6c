import {
  Divider,
  IconAddFriendBulkGreen,
  Text,
} from "@kickavenue/ui/components"
import React from "react"

export interface THistoryItem {
  name: string
  date: string
  amount: string
}

export default function HistoryItem(props: THistoryItem) {
  const { name, date, amount } = props
  return (
    <div className="w-full">
      <div className="flex items-center p-xs">
        <IconAddFriendBulkGreen className="mr-md scale-150" />
        <div className="flex flex-col">
          <Text size="base" state="primary" type="bold">
            {name}
          </Text>
          <Text size="base" state="secondary" type="regular">
            {date}
          </Text>
        </div>
        <Text size="base" state="success" type="bold" className="ml-auto">
          + IDR {amount}
        </Text>
      </div>
      <div className="py-2">
        <Divider orientation="horizontal" />
      </div>
    </div>
  )
}
