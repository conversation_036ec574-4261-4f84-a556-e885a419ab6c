import React, {
  forwardRef,
  useImperative<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useMemo,
  useEffect,
} from "react"
import { Divider } from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"
import { cx } from "class-variance-authority"
import { snakeCase } from "lodash"

import { event } from "@lib/gtag"

import {
  FilterGroupProps,
  isDefaultFilterGroupProps,
  isPriceRangeFilterGroupProps,
} from "./types"
import { useFilterLogic } from "./hooks/useFilterLogic"
import { FilterHeader } from "./components/FilterHeader"
import { SearchInput } from "./components/SearchInput"
import { FilterContent } from "./components/FilterContent"
import { ShowAllButton } from "./components/ShowAllButton"
import PriceRangeFilter from "./components/PriceRangeInputs"
import { updateFromURL } from "./components/FilterItem"
import ShowMoreButton from "./components/ShowMoreButton"

export interface FilterGroupHandle {
  forceUpdate: () => void
  reset: () => void
}

const FilterGroup = forwardRef<FilterGroupHandle, FilterGroupProps>(
  (props, ref) => {
    const { title, showSearch = false, maxItems = 9, onFilterChange } = props
    const searchParams = useSearchParams()
    const filterLogic = useFilterLogic(props, title, maxItems, onFilterChange)

    useImperativeHandle(
      ref,
      () => ({
        forceUpdate: filterLogic.parseParamsFromURL,
        reset: filterLogic.reset,
      }),
      [filterLogic],
    )

    useEffect(
      () => {
        const { selectedItems, minPrice, maxPrice } = updateFromURL(
          searchParams,
          props,
          title,
        )

        if (
          JSON.stringify(selectedItems) !==
          JSON.stringify(filterLogic.selectedItems)
        ) {
          filterLogic.setSelectedItems(selectedItems)
        }

        if (isPriceRangeFilterGroupProps(props)) {
          if (minPrice !== filterLogic.minPrice) {
            filterLogic.setMinPrice(minPrice)
          }
          if (maxPrice !== filterLogic.maxPrice) {
            filterLogic.setMaxPrice(maxPrice)
          }
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [searchParams, props, title],
    )

    const handlePriceChange = useCallback(
      (min: string, max: string) => {
        if (isPriceRangeFilterGroupProps(props) && props.onPriceChange) {
          props.onPriceChange(Number(min) || 0, Number(max) || 0)
        }
        filterLogic.handlePriceChange(min, max)

        event({
          action: "product_searched",
          params: {
            [snakeCase("price_range")]: `${min}-${max}`,
          },
        })
      },
      [filterLogic, props],
    )

    const renderPriceRangeInputs = useMemo(() => {
      if (isPriceRangeFilterGroupProps(props)) {
        return (
          <PriceRangeFilter
            currency={props.currency}
            handlePriceChange={handlePriceChange}
            minPrice={filterLogic.minPrice}
            maxPrice={filterLogic.maxPrice}
          />
        )
      }
      return null
    }, [props, filterLogic, handlePriceChange])

    const filterContent = useMemo(
      () =>
        renderFilterContent(
          filterLogic,
          props,
          showSearch,
          maxItems,
          renderPriceRangeInputs,
        ),
      [filterLogic, props, showSearch, maxItems, renderPriceRangeInputs],
    )

    return (
      <div className={cx("my-4", props.className)}>
        <FilterHeader
          title={title}
          isOpen={filterLogic.isOpen}
          toggleOpen={filterLogic.toggleOpen}
        />
        {filterContent}
        <Divider orientation="horizontal" className="!mt-4" />
      </div>
    )
  },
)

const renderFilterContent = (
  filterLogic: ReturnType<typeof useFilterLogic>,
  props: FilterGroupProps,
  showSearch: boolean,
  maxItems: number,
  renderPriceRangeInputs: React.ReactNode,
): React.ReactNode => {
  if (!filterLogic.isOpen) return null
  return (
    <>
      <SearchInput
        showSearch={showSearch}
        isDefaultFilterGroupProps={isDefaultFilterGroupProps(props)}
        title={props.title}
        searchTerm={filterLogic.searchTerm}
        handleSearchChange={filterLogic.handleSearchChange}
      />
      {renderFilterBody(filterLogic, props, renderPriceRangeInputs)}
      <ShowAllButton
        filteredItems={filterLogic.filteredItems}
        maxItems={maxItems}
        showAll={filterLogic.showAll}
        toggleShowAll={filterLogic.toggleShowAll}
      />
      <ShowMoreButton
        showMore={props?.showMoreButton}
        onClick={props?.onClickShowMore}
      />
    </>
  )
}

const renderFilterBody = (
  filterLogic: ReturnType<typeof useFilterLogic>,
  props: FilterGroupProps,
  renderPriceRangeInputs: React.ReactNode,
): React.ReactNode => {
  if (isPriceRangeFilterGroupProps(props)) {
    return renderPriceRangeInputs
  }
  return (
    <FilterContent
      props={props}
      displayedItems={filterLogic.displayedItems}
      selectedItems={filterLogic.selectedItems}
      handleItemClick={filterLogic.handleItemClick}
      currentSizeType={filterLogic.currentSizeType}
      setCurrentSizeType={filterLogic.setCurrentSizeType}
    />
  )
}

FilterGroup.displayName = "FilterGroup"
export default FilterGroup
