import React from "react"
import { Heading, Text } from "@kickavenue/ui/components"

import { Collection } from "@app/types/collection"

interface CollectionHeaderProps {
  collection?: Collection
}

export const CollectionHeader: React.FC<CollectionHeaderProps> = ({
  collection,
}) => {
  return (
    <div className="my-2 flex flex-col items-center bg-white px-4 py-6">
      <Heading as="h4" heading="4" textStyle="bold">
        {collection?.name || "Collection Name"}
      </Heading>
      <Text size="base" state="secondary" type="regular" className="mt-2">
        {collection?.description || "Lorem ipsum dolor sit amet consectetur."}
      </Text>
    </div>
  )
}
