import { Button, IconArrowRightOutline } from "@kickavenue/ui/components/index"

import useWindowSize from "@app/hooks/useWindowSize"
import { MiscConstant } from "@constants/misc"

const PaymentStatusActions = ({
  cancelText,
  okText,
  disabledCancel,
  disabledOk,
  cancelAction,
  okAction,
}: {
  cancelText: string
  okText: string
  disabledCancel: boolean
  disabledOk: boolean
  cancelAction: () => void
  okAction: () => void
}) => {
  const { size } = useWindowSize()

  const isMobile = size === MiscConstant.WINDOW_SIZE.XS

  const buttonSize = isMobile ? "md" : "lg"

  return (
    <div className="flex w-full justify-between gap-base px-lg pb-lg">
      <Button
        size={buttonSize}
        variant="secondary"
        style={{ width: "100%" }}
        onClick={cancelAction}
        disabled={disabledCancel}
      >
        {cancelText}
      </Button>
      <Button
        size={buttonSize}
        variant="primary"
        style={{ width: "100%" }}
        IconRight={() => <IconArrowRightOutline className="text-gray-w-40" />}
        onClick={okAction}
        disabled={disabledOk}
      >
        {okText}
      </Button>
    </div>
  )
}

export default PaymentStatusActions
