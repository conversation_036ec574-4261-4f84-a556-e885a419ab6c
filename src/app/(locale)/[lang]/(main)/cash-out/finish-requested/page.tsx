"use client"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import KickCreditInfoModal from "@components/KickCredit/components/KickCreditInfoModal"
import SellerCreditInfoModal from "@components/SellerCredit/components/SellerCreditInfoModal"
import CashOutFinishRequested from "@components/CashOut/FinishRequested/CashOutFinishRequested"

const CashOutFinishRequestedPage = () => {
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <CashOutFinishRequested />
      <KickCreditInfoModal />
      <SellerCreditInfoModal />
    </AuthRedirectWrapper>
  )
}

export default CashOutFinishRequestedPage
