import { useRouter } from "next/navigation"
import { useState } from "react"

import { AuthService } from "@application/services/authService"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { event } from "@lib/gtag"
import { useRegistrationStore } from "@stores/registrationStore"

import { useRegisterFormValidation } from "./useRegisterFormValidation"

const useAuthService = () => new AuthService()

export const useRegisterForm = () => {
  const authService = useAuthService()
  const router = useRouter()
  const { registrationData, setRegistrationData } = useRegistrationStore()

  const [error, setError] = useState("")
  const { isFormValid } = useRegisterFormValidation(registrationData)

  const handleInputChange = (field: string) => (value: string) => {
    setRegistrationData({ [field]: value })
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    if (registrationData.password !== registrationData.passwordConfirmation) {
      setError("Passwords do not match")
      window.scrollTo(0, 0)
      return
    }

    try {
      const result = await authService.register(registrationData)
      if (result.error) {
        setError(result.error || "An error occurred during registration")
        window.scrollTo(0, 0)
        return
      }

      if (result.success) {
        const updatedData = {
          ...registrationData,
          registrationTimestamp: new Date().toISOString(),
          otpRequestTimestamp: new Date().toISOString(),
          otpVerified: false,
          createdSource: "WEB",
        }

        event({
          action: "user_registered",
          params: {
            email: updatedData.email,
          },
          userId: updatedData.id || "",
        })

        setRegistrationData(updatedData)
        router.push(PageRouteConstant.VERIFICATION_CODE)
      }
    } catch (err) {
      let errorMessage = "An error occurred during registration"
      if (err instanceof Error) {
        const errorWithResponse = err as {
          status?: number
          response?: { data: { Message: string } }
        }
        if (errorWithResponse.status === 409) {
          errorMessage =
            errorWithResponse.response?.data.Message ||
            "User with this email already exists"
        } else if (errorWithResponse.response?.data.Message) {
          errorMessage = errorWithResponse.response.data.Message
        }
      }
      setError(errorMessage)
      window.scrollTo(0, 0)
    }
  }

  return {
    error,
    isFormValid,
    registrationData,
    handleInputChange,
    handleRegister,
  }
}
