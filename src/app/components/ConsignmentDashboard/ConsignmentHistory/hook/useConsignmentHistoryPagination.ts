import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { useConsignmentHistoryStore } from "stores/consignmentHistoryStore"
import { TTransactionDetailFilter } from "types/transactionDetail.type"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT } = MiscConstant.PAGING_DEFAULT

const useConsignmentHistoryPagination = () => {
  const { filter } = useConsignmentHistoryStore()
  const { handleChangeQuery } = useURLQuery()
  const { page, pageSize, totalPages } = (filter ||
    {}) as TTransactionDetailFilter

  const handleNext = () => {
    const nextPage = page + 1
    handleChangeQuery(PAGE, nextPage)
  }

  const handlePrev = () => {
    const prevPage = page - 1
    handleChangeQuery(PAGE, prevPage)
  }

  const handlePage = (page: number) => {
    handleChangeQuery(PAGE, page - 1)
  }

  const handlePerPage = (size: number) => {
    handleChangeQuery(PAGE_SIZE, size)
    handleChangeQuery(PAGE, PAGE_DEFAULT)
  }

  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useConsignmentHistoryPagination
