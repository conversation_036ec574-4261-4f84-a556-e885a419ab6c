/* eslint-disable @typescript-eslint/naming-convention */

"use client"

import { Divider, Heading, Text } from "@kickavenue/ui/components"
import Image from "next/image"
import React from "react"

interface BlogContent {
  id: number
  title: string
  subtitle: string
  slug: string
  startTime: string
  endTime: string
  content: string
  author: string
  image: string
  status: string
  tags: string
  seoTitleIna: string
  seoKeywordIna: string
  seoDescriptionIna: string
  seoTitleEng: string
  seoKeywordEng: string
  seoDescriptionEng: string
  relatedItem: any
  isActive: boolean
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
}

export interface BlogContentProps {
  data: BlogContent
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  })
}

const BlogContentComponent: React.FC<BlogContentProps> = ({ data }) => (
  <div className="container mx-auto gap-4 py-8">
    <div className="flex justify-center">
      <div className="w-full max-w-[1440px]">
        <div className="col-span-12">
          <Heading heading="2" textStyle="bold" className="mb-1">
            {data.title}
          </Heading>
          <Heading heading="4" textStyle="regular">
            By {data.author} - {formatDate(data.createdAt)}
          </Heading>
        </div>

        <div className="col-span-12 mt-8">
          <div
            className="prose prose-lg max-w-none !font-plus-jakarta text-base font-normal text-gray"
            dangerouslySetInnerHTML={{ __html: data.content }}
          />
        </div>
      </div>
      <div className="mx-6">
        <Divider orientation="vertical" className="h-full" />
      </div>
      <BlogSidebar title={data.title} />
    </div>
  </div>
)

const BlogSidebar: React.FC<{ title: string }> = ({ title }) => (
  <div className="flex flex-col">
    <Heading heading="4" textStyle="bold">
      Buy on Kick Avenue
    </Heading>
    <div className="mt-6">
      <ProductInfo title={title} />
      <ProductDetails />
    </div>
  </div>
)

const ProductInfo: React.FC<{ title: string }> = ({ title }) => (
  <div className="flex gap-4">
    <Image
      src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
      alt={title || ""}
      width={100}
      height={100}
      className="rounded-sm"
    />
    <div className="flex flex-col gap-1">
      <Text size="base" state="secondary" type="medium">
        On Running Cloudmonster All Black
      </Text>
      <Text size="base" state="secondary" type="medium">
        IDR 2,750,000
      </Text>
      <Text size="xs" state="secondary" type="strikethrough">
        IDR 2,900,000
      </Text>
    </div>
  </div>
)

const ProductDetails: React.FC = () => (
  <div className="mt-6 flex flex-col gap-4">
    <Text size="base" state="secondary" type="bold">
      Product Detail
    </Text>
    <DetailItem label="SKU" value="61.99025" />
    <Divider orientation="horizontal" />
    <DetailItem label="Color" value="All Black" />
    <Divider orientation="horizontal" />
    <DetailItem label="Release Date" value="-" />
    <Divider orientation="horizontal" />
    <DetailItem label="Retail Price" value="$170 USD (approx.)" />
  </div>
)

const DetailItem: React.FC<{ label: string; value: string }> = ({
  label,
  value,
}) => (
  <div className="flex justify-between">
    <Text size="sm" state="secondary" type="regular">
      {label}
    </Text>
    <Text size="sm" state="secondary" type="regular">
      {value}
    </Text>
  </div>
)

export default BlogContentComponent
