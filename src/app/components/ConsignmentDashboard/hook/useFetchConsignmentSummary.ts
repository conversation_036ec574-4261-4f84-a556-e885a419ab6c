import { useQuery } from "@tanstack/react-query"

import { GetConsignmentSummary } from "@application/usecases/getConsignmentSummary"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { TransactionDetailApiRepository } from "@infrastructure/repositories/transactionDetailApiRepository"

const useFetchConsignmentSummary = () => {
  const fetchConsignmentSummary = async () => {
    const r = new TransactionDetailApiRepository()
    const u = new GetConsignmentSummary(r)
    const res = await u.execute()
    return res
  }

  const { data, isLoading } = useQuery({
    queryKey: [QueryKeysConstant.GET_CONSIGNMENT_SUMMARY],
    queryFn: fetchConsignmentSummary,
    retry: false,
    staleTime: 1,
  })

  return { data, isLoading }
}

export default useFetchConsignmentSummary
