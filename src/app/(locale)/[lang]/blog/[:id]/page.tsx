import Image from "next/image"

import CategoryBlog from "../components/CategoryBlog"
import BlogContent, { BlogContentProps } from "../components/BlogContent"

const BlogDetail = () => {
  const dummyBlogData: BlogContentProps = {
    data: {
      id: 1,
      title: "The Ultimate Guide to Sneaker Authentication",
      subtitle: "Learn how to spot authentic sneakers and avoid counterfeits",
      slug: "ultimate-guide-sneaker-authentication",
      startTime: "2024-01-01T00:00:00Z",
      endTime: "2024-12-31T23:59:59Z",
      content: `
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
          </p>`,
      author: "KickAvenue Team",
      image: "/images/blog/sneaker-authentication-guide.jpg",
      status: "published",
      tags: "sneakers,authentication,guide,tips",
      seoTitleIna: "Panduan Lengkap Autentikasi Sneakers",
      seoKeywordIna: "autentikasi sneakers,panduan sneakers,tips sneakers asli",
      seoDescriptionIna:
        "Pelajari cara mengidentifikasi sneakers asli dan menghindari barang palsu dengan panduan lengkap dari tim ahli kami.",
      seoTitleEng: "The Ultimate Guide to Sneaker Authentication",
      seoKeywordEng: "sneaker authentication,sneaker guide,authentic sneakers",
      seoDescriptionEng:
        "Learn how to identify authentic sneakers and avoid counterfeits with our comprehensive guide from our expert team.",
      relatedItem: [],
      isActive: true,
      createdAt: "2024-01-15T08:00:00Z",
      createdBy: "admin",
      updatedAt: "2024-01-15T08:00:00Z",
      updatedBy: "admin",
    },
  }
  return (
    <div className="mb-6">
      <Image
        src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
        alt="image"
        width={1440}
        height={450}
        style={{ width: "100%", height: "450px", objectFit: "cover" }}
      />
      <BlogContent data={dummyBlogData.data} />
      <CategoryBlog image={false} />
      <CategoryBlog image={false} />
    </div>
  )
}

export default BlogDetail
