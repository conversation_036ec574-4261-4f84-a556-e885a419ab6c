import { CheckValidCardNumberRepository } from "@domain/interfaces/CheckValidCardNumberRepository"
import { checkValidCardNumberApi } from "@infrastructure/api/checkValidCardNumberApi"
import { BINData } from "types/bin.type"

export class CheckValidCardNumberAPIRepository
  implements CheckValidCardNumberRepository
{
  async getByBinNumber(binNumber: string): Promise<BINData> {
    const res = await checkValidCardNumberApi.getByBinNumber(binNumber)
    if (!res?.data) {
      throw new Error("Failed to get bin number")
    }
    return res.data as BINData
  }
}
