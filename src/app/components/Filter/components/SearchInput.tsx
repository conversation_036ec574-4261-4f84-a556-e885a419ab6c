import React from "react"

import SharedSearchInput from "@components/shared/SearchInput"

interface SearchInputProps {
  showSearch: boolean
  isDefaultFilterGroupProps: boolean
  title: string
  searchTerm: string
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export const SearchInput: React.FC<SearchInputProps> = ({
  showSearch,
  isDefaultFilterGroupProps,
  title,
  searchTerm,
  handleSearchChange,
}) => {
  if (!showSearch || !isDefaultFilterGroupProps) return null

  const handleClearSearch = () => {
    handleSearchChange({
      target: { value: "" },
    } as React.ChangeEvent<HTMLInputElement>)
  }

  return (
    <SharedSearchInput
      placeholder={`Search ${title}`}
      value={searchTerm}
      onChange={handleSearchChange}
      className="mb-3"
      onClearText={handleClearSearch}
    />
  )
}
