import { ReadonlyURLSearchParams } from "next/navigation"
import { DateRange } from "react-day-picker"

import { formatPrice as formatPriceUtils } from "@utils/misc"
import { Brand as BrandType } from "types/brand.type"
import { TCategory } from "types/category.type"
import {
  TListingItemFilterOption,
  TListingItemFilterOptionValue,
  TListingItemFilterState,
  TListingItemFilterStateKey,
  TListingItemFilterStateValue,
} from "types/listingItem.type"
import { TUniqueSize, TUniqueSizeFilter } from "types/sizeChart.type"
import { MiscConstant } from "@constants/misc"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import { EBuyingDashboardTab } from "../BuyingDashboard/buyingDashboard.type"
import { ESellingDashboardTab } from "../SellingDashboard/sellingDashboard.type"
import { EConsignmentDashboardTab } from "../ConsignmentDashboard/consignmentDashboard.type"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const { Categories, CategoryId, Brand, BrandId, Size, SizeId } =
  TListingItemFilterStateKey

const { Offers, BuyingPending, BuyingInProgress, BuyingHistory } =
  EBuyingDashboardTab

const { Current, InProgress, SellingHistory } = ESellingDashboardTab
const { Pending, Active, CgInProgress, History } = EConsignmentDashboardTab

export type FilterState = Record<string, string | string[]>

export const getTabNames = (selectedTab: string): string[] => {
  switch (selectedTab) {
    case Offers:
      return [
        "Offer Price",
        "Created Date",
        "Single Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case BuyingPending:
      return [
        "Purchase Amount",
        "Purchase Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case BuyingHistory:
    case BuyingInProgress:
      return [
        "Status",
        "Purchase Amount",
        "Purchase Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case Current:
      return [
        "Status",
        "Has Offer",
        "Listing Price",
        "Created Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case InProgress:
      return [
        "Status",
        "Sold Price",
        "Sold Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case CgInProgress:
      return [
        "Sold Price",
        "Sold Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case History:
      return [
        "Status",
        "Sold Price",
        "Sold Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case SellingHistory:
      return [
        "Status",
        "Sold Price",
        "Sold Date",
        "Condition",
        "Size",
        "Categories",
        "Brand",
      ]
    case Pending:
      return ["Status", "Price", "Condition", "Size", "Categories", "Brand"]
    case Active:
      return [
        "Has Offer",
        "Listing Price",
        "Created Date",
        "Condition",
        "Size",
        "Categories",
      ]
    default:
      return []
  }
}

export const getFilterCount = (
  tabName: string,
  filterState: TListingItemFilterState,
): number => {
  const key = tabName.toLowerCase().replace(" ", "")
  switch (key) {
    case "offerprice":
    case "purchaseamount":
      return filterState[`${key}Min`] || filterState[`${key}Max`] ? 1 : 0
    case "createddate":
    case "purchasedate":
      return filterState[`${key}Start`] || filterState[`${key}End`] ? 1 : 0
    default:
      return filterState[key] ? (filterState[key] as string[]).length : 0
  }
}

export const formatPrice = (price: string): string => {
  return formatPriceUtils(Number(price), null, "IDR")
}

export const formatDateForChip = (dateString: string): string => {
  if (!dateString) return ""
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("id-ID", {
    day: "numeric",
    month: "long",
    year: "numeric",
  }).format(date)
}

export const isValidDateString = (dateString: string): boolean => {
  return !isNaN(Date.parse(dateString))
}

export const parseFilterValue = (value: string): string | string[] => {
  try {
    const parsed = JSON.parse(value)
    return Array.isArray(parsed) ? parsed : value
  } catch {
    return value
  }
}

export const mapHasOfferOptions = (options: string[]): string[] =>
  options.map((option) => `Has Offer: ${option}`)

export const mapOfferFilterStateToOptions = (
  filterState: TListingItemFilterState,
): string[] => {
  const hasOffer = (filterState?.hasoffer as string[])?.[0]?.replaceAll(
    "Has Offer: ",
    "",
  )
  if (!hasOffer) return []
  return [hasOffer]
}

export const isSizeFilterSelected = (
  size: TUniqueSize,
  selectedSizes: TListingItemFilterOption[],
): boolean => {
  return selectedSizes.some((selectedSize) => {
    return size.us === selectedSize.label
  })
}

export const isFilterSelected = (
  selectedFilter: TListingItemFilterOption[],
  toggledFilter: TCategory | BrandType | TUniqueSizeFilter,
): boolean => {
  return Boolean(
    selectedFilter.find((c) => c.value === toggledFilter.id?.toString()),
  )
}

export const getSizeNewSelectedFilters = (
  prevSelected: TListingItemFilterOption[],
  toggledFilter: TUniqueSize,
): TListingItemFilterOption[] => {
  return isSizeFilterSelected(toggledFilter, prevSelected)
    ? prevSelected.filter((c) => c.label !== toggledFilter.us)
    : [
        ...prevSelected,
        {
          label: toggledFilter.us as string,
          value: toggledFilter.id?.toString(),
        },
      ]
}

export const getNewSelectedFilters = (
  prevSelected: TListingItemFilterOption[],
  toggledFilter: TCategory | BrandType | TUniqueSizeFilter,
): TListingItemFilterOption[] => {
  const toggledFilterId =
    typeof toggledFilter.id === "string"
      ? toggledFilter.id
      : toggledFilter.id?.toString()

  return isFilterSelected(prevSelected, toggledFilter)
    ? prevSelected.filter((c) => c.value !== toggledFilterId)
    : [
        ...prevSelected,
        {
          label: toggledFilter.name as string,
          value: toggledFilterId,
        },
      ]
}

export const getFilterStateValue = (
  value?: TListingItemFilterOption | string,
) => {
  if (!value) return ""
  if (typeof value === "string") return value
  return value.label
}

export const stringifyFilterOption = (
  sourceKey: "label" | "value",
  value?: TListingItemFilterStateValue,
): string => {
  const mapValue = (value: TListingItemFilterOptionValue) => {
    if (typeof value === "string") return value
    return value[sourceKey]
  }

  if (!value) return ""
  if (typeof value === "string") return value

  return value.map((v) => mapValue(v)).join(",")
}

export const createFilterParams = ({
  filterState,
}: {
  filterState: Record<string, TListingItemFilterStateValue>
}) => {
  const params = new URLSearchParams()
  const skipKeys = [CategoryId, BrandId, SizeId]

  for (const [key, value] of Object.entries(filterState)) {
    if (!value || skipKeys.includes(key as TListingItemFilterStateKey)) continue
    if (Array.isArray(value) && value.length === 0) continue

    if (key === Categories) {
      params.set(Categories, stringifyFilterOption("label", value))
      params.set(CategoryId, stringifyFilterOption("value", value))
      continue
    }

    if (key === Brand) {
      params.set(Brand, stringifyFilterOption("label", value))
      params.set(BrandId, stringifyFilterOption("value", value))
      continue
    }

    if (key === Size) {
      params.set(Size, stringifyFilterOption("label", value))
      params.set(SizeId, stringifyFilterOption("value", value))
      continue
    }

    if (Array.isArray(value)) {
      params.set(key, value.join(","))
      continue
    }
    params.set(key, value)
  }

  params.set(PAGE, PAGE_DEFAULT.toString())
  params.set(PAGE_SIZE, PAGE_SIZE_DEFAULT.toString())

  return { params }
}

export const isFilterStateEmpty = (
  filterState?: TListingItemFilterState | null,
) => {
  if (!filterState) return false
  return Object.keys(filterState)?.length === 0
}

const mapParamsWithIds = (
  values: string,
  ids: string | null,
): TListingItemFilterOption[] => {
  const items = values.split(",")
  const itemIds = (ids || "").split(",")

  return items.map((item, index) => ({
    label: item,
    value: itemIds[index],
  }))
}

export const mapParamsToFilterState = (params: ReadonlyURLSearchParams) => {
  const filterState: TListingItemFilterState = {}
  const skipKeys = [CategoryId, BrandId]
  for (const [key, value] of params.entries()) {
    if (!value) continue

    if (key === Categories) {
      filterState[key] = mapParamsWithIds(value, params.get(CategoryId))
      continue
    }

    if (key === Brand) {
      filterState[key] = mapParamsWithIds(value, params.get(BrandId))
      continue
    }

    if (key === Size) {
      filterState[key] = mapParamsWithIds(value, params.get(SizeId))
      continue
    }

    if (skipKeys.includes(key as TListingItemFilterStateKey)) continue

    filterState[key] = value.split(",")
  }
  return filterState
}

export const mapParamsToFilterDateRange = (params: ReadonlyURLSearchParams) => {
  const dateRanges: Record<string, DateRange | undefined> = {}
  for (const [key, value] of params.entries()) {
    const lowerKey = key.toLowerCase()
    if (!value) continue
    if (lowerKey.includes("date") && lowerKey.includes("start")) {
      const endKey = key.replace("Start", "End")
      const rawKey = key.replace("Start", "")
      const end = params.get(endKey)
      dateRanges[rawKey] = {
        from: new Date(value),
        to: end ? new Date(end) : undefined,
      }
      continue
    }
  }
  return dateRanges
}

export const isFilterStateContains = (
  filterState: TListingItemFilterState,
  key: string,
) => {
  return Object.keys(filterState || {}).includes(key)
}

export const clearFilterStateWithKey = (
  filterState: TListingItemFilterState,
  key: TListingItemFilterStateKey,
) => {
  const removedKeys = Object.keys(filterState || {}).filter((k) =>
    [key].includes(k as TListingItemFilterStateKey),
  )
  const newFilterState: TListingItemFilterState = {}
  for (const [key, value] of Object.entries(filterState || {})) {
    if (removedKeys.includes(key)) continue
    newFilterState[key] = value
  }
  return newFilterState
}

export const getDefaultDashboardTab = () => {
  const { SELLING_DASHBOARD, CONSIGNMENT_DASHBOARD, PROFILE_BUYING } =
    PageRouteConstant

  const { Offers } = EBuyingDashboardTab
  const { Pending } = EConsignmentDashboardTab
  const { Current } = ESellingDashboardTab

  if (typeof window === "undefined") return null

  const currentPath = window.location.pathname
  if (currentPath.includes(SELLING_DASHBOARD)) return Current
  if (currentPath.includes(CONSIGNMENT_DASHBOARD)) return Pending
  if (currentPath.includes(PROFILE_BUYING)) return Offers

  return null
}

export const getDashboardTab = ({
  searchParams,
}: {
  searchParams: ReadonlyURLSearchParams
}) => {
  const { TAB } = MiscConstant.FILTER_FIELDS
  const tab = searchParams.get(TAB) || getDefaultDashboardTab()
  return tab
}
