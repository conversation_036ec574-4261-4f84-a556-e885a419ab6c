"use client"

import NavigationOption from "@kickavenue/ui/components/NavigationOption"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect } from "react"

import {
  isRouterSelected,
  listSideBar,
} from "@app/components/Profile/sidebar.utils"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { useSellingCurrentStore } from "stores/sellingCurrentStore"

const isShowTitle = (
  pathname: string,
  currentPathName: string,
  title: string,
) => {
  if (pathname === currentPathName) {
    return title
  }
  return ""
}
export const SidebarProfile = () => {
  const pathname = usePathname()
  const { setSelectedRowKeys: setSelectedRowKeysBuying } = useBuyingOfferStore()
  const { setSelectedRowKeys: setSelectedRowKeysSelling } =
    useSellingCurrentStore()

  useEffect(() => {
    window.scroll(0, 0)
    setSelectedRowKeysBuying([])
    setSelectedRowKeysSelling([])
  }, [pathname, setSelectedRowKeysBuying, setSelectedRowKeysSelling])

  return (
    <>
      <div className="hidden rounded-base bg-white lg:block">
        {listSideBar.map((item) => {
          return (
            <Link key={item.pathname} href={item.pathname} scroll>
              <NavigationOption
                selected={isRouterSelected(item.pathname, pathname)}
                iconLeading={item.icon}
                subTitle={item.subTitle}
                title={item.title}
                type="default"
                weight="bold"
                subTitleProps={{
                  size: "sm",
                  state: "secondary",
                  type: "regular",
                }}
                className="[&>div>div>svg]:!shrink-0"
              />
            </Link>
          )
        })}
      </div>
      <div className="mx-auto mt-lg flex overflow-scroll rounded-sm bg-white lg:hidden">
        {listSideBar.map((item) => {
          return (
            <Link key={item.pathname} href={item.pathname} scroll>
              <NavigationOption
                selected={isRouterSelected(item.pathname, pathname)}
                className="whitespace-nowrap"
                iconLeading={item.icon}
                title={isShowTitle(item.pathname, pathname, item.title)}
                type="default"
                weight="bold"
                subTitleProps={{
                  size: "sm",
                  state: "secondary",
                  type: "regular",
                }}
              />
            </Link>
          )
        })}
      </div>
    </>
  )
}
