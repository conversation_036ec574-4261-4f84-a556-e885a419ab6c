import React, { useRef, useCallback } from "react"

interface OTPInputFieldProps {
  otpValues: string[]
  error: string | null
  handleInputChange: (value: string, index: number) => void
  handleKeyDown: (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number,
  ) => void
  handlePaste: (e: React.ClipboardEvent<HTMLInputElement>) => void
  type: "EMAIL" | "SMS"
  disabled?: boolean
}

const OTPInputField: React.FC<OTPInputFieldProps> = ({
  otpValues,
  error,
  handleInputChange,
  handleKeyDown,
  handlePaste,
  type,
  disabled = false,
}) => {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  const renderInput = useCallback(
    (value: string, index: number) => {
      return (
        <input
          key={`${type}-otp-input-${index}`}
          ref={(el) => {
            inputRefs.current[index] = el
          }}
          className={`text-xl size-12 rounded-md border text-center focus:outline-none ${
            error ? "border-danger" : "border-gray-w-80 focus:border-gray-b-65"
          }`}
          type="text"
          inputMode="numeric"
          autoComplete="one-time-code"
          maxLength={1}
          value={value}
          onChange={(e) => handleInputChange(e.target.value, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          onFocus={(e) => e.currentTarget.select()}
          aria-label={`${type} OTP digit ${index + 1}`}
          name={`${type.toLowerCase()}-otp-${index}`}
          disabled={disabled}
        />
      )
    },
    [error, handleInputChange, handleKeyDown, handlePaste, type, disabled],
  )

  return (
    <div
      className="flex justify-center gap-2"
      role="group"
      aria-label={`${type} OTP Input`}
    >
      {otpValues.map(renderInput)}
    </div>
  )
}

export default React.memo(OTPInputField)
