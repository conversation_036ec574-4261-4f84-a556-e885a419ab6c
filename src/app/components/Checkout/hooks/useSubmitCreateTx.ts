import { useMutation } from "@tanstack/react-query"

import { SaveTransaction } from "@application/usecases/saveTransaction"
import { TransactionApiRepository } from "@infrastructure/repositories/transactionApiRepository"
import { TTransaction, TTransactionBodyParams } from "types/transaction.type"

const useSubmitCreateTx = ({
  onSuccess,
  onError,
}: {
  onSuccess?: (data: TTransaction) => void
  onError?: (error: Error) => void
}) => {
  const submitCreateTx = async (payload: TTransactionBodyParams) => {
    const r = new TransactionApiRepository()
    const u = new SaveTransaction(r)
    const res = await u.execute(payload)
    return res
  }

  const mutation = useMutation({
    mutationFn: submitCreateTx,
    onSuccess: (data) => {
      onSuccess?.(data)
    },
    onError: (error) => {
      onError?.(error)
    },
  })

  return mutation
}

export default useSubmitCreateTx
