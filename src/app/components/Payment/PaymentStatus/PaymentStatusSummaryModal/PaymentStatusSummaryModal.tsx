import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import { TTransaction } from "types/transaction.type"
import { TTransactionDetail } from "types/transactionDetail.type"

import PaymentStatusSummaryModalContent from "./PaymentStatusSummaryModalContent"

const { PAYMENT_SUMMARY_PAYMENT } = ModalConstant.MODAL_IDS

const PaymentStatusSummaryModal = ({
  transaction,
  txDetails,
}: {
  transaction?: TTransaction | null
  txDetails?: TTransactionDetail[] | null
}) => {
  const { setOpen, open, modalId } = useModalStore()

  const handleClose = () => {
    setOpen(false, PAYMENT_SUMMARY_PAYMENT)
  }

  const isOpen = open && modalId === PAYMENT_SUMMARY_PAYMENT

  if (!isOpen) return null

  return (
    <Modal modalId={PAYMENT_SUMMARY_PAYMENT} onClose={handleClose}>
      <HeaderModal title="Payment Summary" onClose={handleClose} />
      <PaymentStatusSummaryModalContent
        transaction={transaction}
        txDetails={txDetails}
        isOpen={isOpen}
      />
    </Modal>
  )
}

export default PaymentStatusSummaryModal
