import { TEarnHistory } from "types/earnHistory.type"
import { TInvitedFriend } from "types/invitedFriend.type"
import { TReferral } from "types/referral.type"

export interface TabMenu {
  id: number
  name: string
}

export interface InviteAndEarnProps {
  earnHistory: TEarnHistory[]
  referral: TReferral
  invitedFriend: TInvitedFriend[]
}

export const tabItems: TabMenu[] = [
  {
    id: 0,
    name: "Invite",
  },
  {
    id: 1,
    name: "Earn",
  },
]

export const inviteTopHeaderText = "Share Referral Link, Earn Rewards!"

export const inviteTopBodyText =
  "Lorem ipsum dolor sit amet consectetur. Consequat rhoncus in vulputate non vel neque libero lectus quam. Pellentesque imperdiet id hendrerit tellus. Bibendum elit lacinia diam neque eu. Molestie elementum mattis senectus risus nunc lacinia sit hendrerit placerat."
