import HelpCenterFooter from "@components/HelpCenter/HelpCenterFooter"
import Help<PERSON>enterHeader from "@components/HelpCenter/HelpCenterHeader"
import HelpCenterSection from "@components/HelpCenter/HelpCenterSection"

export default function HelpCenterSearchLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      <HelpCenterSection
        parentClassName="bg-white shadow-[0px_0px_8px_0px_rgba(36,36,36,0.08)]"
        childClassName="flex flex-wrap justify-between gap-sm p-base xl:px-0 xl:py-base"
      >
        <HelpCenterHeader />
      </HelpCenterSection>
      {children}
      <HelpCenterFooter />
    </>
  )
}
