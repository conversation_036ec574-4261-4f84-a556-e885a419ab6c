"use client"

import { useEffect } from "react"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import KickCreditInfoModal from "@components/KickCredit/components/KickCreditInfoModal"
import TopUpStatus from "@components/TopUpConfirmation/TopUpStatus"
import TopUpDetails from "@components/TopUpConfirmation/TopUpDetails"
import TopUpFooter from "@components/TopUpConfirmation/TopUpFooter"
import PaymentDetails from "@components/TopUpConfirmation/PaymentDetails"
import PaymentMethodTopUp from "@components/TopUpConfirmation/PaymentMethodTopUp"
import useTopUpConfirmationForm from "@components/TopUpConfirmation/hooks/useTopUpConfirmationForm"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"
import usePageLeaveTopUpConfirmation from "@components/TopUpConfirmation/hooks/usePageLeaveTopUpConfirmation"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"

const TopUpConfirmationPage = () => {
  const { member } = useMemberStore()
  const { currentBalance, currentFeeNumber } = useGetCurrentBalanceAndFee()

  const {
    disabledConfirmation,
    isShowAmountInputError,
    isShowAmountEligibleInputError,
    topUpAmount,
    topUpFee,
    totalTopUp,
  } = useTopUpConfirmationForm({ feeData: currentFeeNumber })

  // for resetting form on page leave
  usePageLeaveTopUpConfirmation()

  useEffect(() => {
    event({
      action: "user_top_up_started",
      userId: member?.id,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <div className="w-full p-base text-center text-heading-4 font-bold">
        Top Up Confirmation
      </div>
      <div className="bg-gray-w-95 p-xxl pt-lg md:flex md:justify-center">
        <div className="rounded-base bg-white md:w-[612px] md:p-lg">
          <div className="flex flex-col">
            <TopUpStatus
              kickCreditBalance={currentBalance}
              topUpFee={topUpFee}
            />
            <TopUpDetails
              isShowAmountEligibleInputError={isShowAmountEligibleInputError}
              isShowAmountInputError={isShowAmountInputError}
            />
            <PaymentMethodTopUp />
            <PaymentDetails
              topUpAmount={topUpAmount}
              topUpFee={topUpFee}
              totalTopUp={totalTopUp}
            />
            <TopUpFooter disabled={disabledConfirmation} />
          </div>
        </div>
      </div>
      <KickCreditInfoModal />
    </AuthRedirectWrapper>
  )
}

export default TopUpConfirmationPage
