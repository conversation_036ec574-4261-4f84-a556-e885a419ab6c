export const defaultLocale = "en"
export const locales = ["id", "en", "fr"]
export const localePrefix = "as-needed"

export function getLocalePartsFrom(path: string) {
  const pathnameParts = path.split("/")
  const locale = locales.includes(pathnameParts[1])
    ? pathnameParts[1]
    : defaultLocale
  const slugs =
    locale === defaultLocale ? pathnameParts.slice(1) : pathnameParts.slice(2)
  return { locale, slugs }
}
