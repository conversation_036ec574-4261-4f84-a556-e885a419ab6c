"use client"

import React from "react"

import FilterModal from "./components/FilterModal"
import { useFilterDashboard } from "./hooks/useFilterDashboard"

interface FilterDashboardProps {
  openFilterOfferPrice: boolean
  setOpenFilterOfferPrice: (open: boolean) => void
  selectedTab: string
}

const FilterDashboard: React.FC<FilterDashboardProps> = ({
  openFilterOfferPrice,
  setOpenFilterOfferPrice,
  selectedTab,
}) => {
  const {
    tabNames,
    filterState,
    dateRanges,
    setFilterState,
    setDateRanges,
    applyFilters,
    resetFilters,
    handleCloseModal,
    handleRemoveChip,
  } = useFilterDashboard(selectedTab, setOpenFilterOfferPrice)

  return (
    <FilterModal
      openFilterOfferPrice={openFilterOfferPrice}
      setOpenFilterOfferPrice={handleCloseModal}
      tabNames={tabNames}
      filterState={filterState}
      setFilterState={setFilterState}
      applyFilters={applyFilters}
      resetFilters={resetFilters}
      handleRemoveChip={handleRemoveChip}
      dateRanges={dateRanges}
      setDateRanges={setDateRanges}
    />
  )
}

export default FilterDashboard
