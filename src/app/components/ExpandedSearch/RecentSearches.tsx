import { Chip, Text } from "@kickavenue/ui/components"

const RecentSearches = () => {
  const chips = ["yeazy", "air", "adidas", "nike", "airpods max"]

  return (
    <div className="flex w-full flex-col gap-base">
      <div className="flex items-center justify-between">
        <Text size="base" type="bold" state="primary">
          Recent Searches
        </Text>

        <Text
          size={"base"}
          type={"bold"}
          state={"disabled"}
          className="!text-green"
        >
          Clear All
        </Text>
      </div>

      <div className="flex w-full flex-wrap gap-xs">
        {chips.map((chip) => (
          <Chip key={chip} size="sm" isRemovable className="!text-base">
            {chip}
          </Chip>
        ))}
      </div>
    </div>
  )
}

export default RecentSearches
