import { TTransactionDetailFilter } from "types/transactionDetail.type"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import { camelToSnake, isEmpty, isEmptyArray } from "@utils/misc"
import SellingTableEmpty from "@components/SellingDashboard/SellingTableEmpty"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import Table from "@components/shared/Table"
import useFetchMyTxDetails from "@app/hooks/useFetchMyTxDetails"
import TablePagination from "@components/shared/Table/TablePagination"
import { isQueryLoading } from "@utils/network"
import { TSorter } from "types/table.type"
import { useBuyingHistoryStore } from "stores/buyingHistoryStore"
import useURLQuery from "@app/hooks/useUrlQuery"

import { BuyingDashboardConstant } from "../buyingDashboard.contant"

import styles from "./BuyingDashboardHistoryTable.module.scss"
import useBuyingHistoryTable from "./hooks/useBuyingHistoryTable"
import useBuyingHistoryPagination from "./hooks/useBuyingHistoryPagination"

const { SORT_BY } = BuyingDashboardConstant.FILTER_FIELDS

const BuyingDashboardHistoryTable = () => {
  const { list, filter, setList, setFilter } = useBuyingHistoryStore()
  const { handleChangeQuery } = useURLQuery()

  const { columns } = useBuyingHistoryTable()

  const paging = useBuyingHistoryPagination()

  const txQuery = useFetchMyTxDetails({
    onSuccess: (data) => {
      setList(data?.content)
      setFilter({
        ...((filter || {}) as TTransactionDetailFilter),
        totalPages: data?.totalPages,
      })
    },
    filters: filter as TTransactionDetailFilter,
    enabled:
      !isEmpty(filter?.page) &&
      !isEmpty(filter?.pageSize) &&
      !isEmptyArray(filter?.status) &&
      !isEmpty(filter?.sortBy),
  })

  const isLoading = isQueryLoading(txQuery)

  const handleTableChange = (sorter: TSorter) => {
    const field = camelToSnake(sorter.field)?.toLowerCase()
    handleChangeQuery(SORT_BY, `${field},${sorter.order}`)
  }

  if (isLoading) {
    return <SpinnerLoading className="min-h-[calc(100vh-400px)]" />
  }

  if (!isLoading && isEmptyArray(list)) {
    return (
      <SellingTableEmpty
        title="Your Purchase History Awaits"
        subText="Turn browsing into buying! Discover items you love and start filling this page with your unique finds."
        pageLink={PageRouteConstant.WISHLIST}
        btnText="Explore Wishlist"
      />
    )
  }

  return (
    <>
      <div className={styles["table-container"]}>
        <Table
          rowKey="transactionId"
          columns={columns}
          dataSource={list}
          className={styles.table}
          onTableChange={handleTableChange}
          activeSort={filter?.sortBy}
        />
      </div>

      <div className="flex justify-end gap-base">
        <TablePagination {...paging} />
      </div>
    </>
  )
}

export default BuyingDashboardHistoryTable
