import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { useCallback } from "react"

import { ItemConstant } from "@constants/item"
import useSearchFilter from "@hooks/useSearchFilter"
import { getAppliedFilters } from "@utils/search"

import SearchChipItem from "./SearchChipItem"

const { FILTER_KEYS } = ItemConstant

const SearchChips = () => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const { filter } = useSearchFilter()
  const appliedFilters = getAppliedFilters(filter)

  const handleRemoveFilter = useCallback(
    (item: Record<string, string>) => {
      const params = new URLSearchParams(searchParams.toString())

      const pairedKeys: Record<string, string> = {
        brand: "brandID",
        category: "categoryID",
        subcategory: "subcategoryID",
        size: "sizeID",
      }

      for (const [key, value] of Object.entries(filter)) {
        const values = (value as string)?.split(",")
        let filterValue = ""

        const cleaned = item?.value.replace(/[^\d-]/g, "")
        const [startStr, endStr] = cleaned.split("-")
        const start = parseInt(startStr, 10)
        const end = parseInt(endStr, 10)
        const result = start === 0 ? `-${end}` : cleaned

        switch (key) {
          case FILTER_KEYS.PRICE_RANGE:
            filterValue = result
            break
          case FILTER_KEYS.KEYWORD:
            filterValue = item?.value?.replace(/Search: /g, "")
            break
          default:
            filterValue = item?.value
            break
        }

        if (key === item.key) {
          const pairedIDKey = pairedKeys[key]
          if (pairedIDKey && params.has(pairedIDKey)) {
            const labelList = params.get(key)?.split(",") || []
            const idList = params.get(pairedIDKey)?.split(",") || []

            const indexToRemove = labelList.indexOf(filterValue)
            if (indexToRemove !== -1) {
              labelList.splice(indexToRemove, 1)
              idList.splice(indexToRemove, 1)

              if (labelList.length > 0) {
                params.set(key, labelList.join(","))
                params.set(pairedIDKey, idList.join(","))
              } else {
                params.delete(key)
                params.delete(pairedIDKey)
              }
            }
            continue
          }

          const updatedValues = values.filter((v) => v !== filterValue)
          if (updatedValues.length > 0) {
            params.set(key, updatedValues.join(","))
          } else {
            params.delete(key)
          }
        } else {
          params.set(key, value as string)
        }
      }

      router.push(`${pathname}?${params.toString()}`)
    },
    [filter, pathname, router, searchParams],
  )

  if (!appliedFilters.length) return null

  return (
    <>
      <div className="mb-base" />
      <div className="flex justify-center">
        <div className="w-full max-w-[1440px]">
          <div className="flex w-full max-w-[1440px] flex-wrap gap-xs">
            {appliedFilters.map((filter) => (
              <SearchChipItem
                key={filter.value}
                filter={filter}
                handleRemoveItemFilter={handleRemoveFilter}
                handleRemoveAllFilter={() => router.push(pathname)}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  )
}

export default SearchChips
