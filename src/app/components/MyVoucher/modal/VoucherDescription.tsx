import React from "react"
import { Text } from "@kickavenue/ui/components"

export interface VoucherDescriptionProps {
  description: string
  termAndConditions?: string
}

export default function VoucherDescription(props: VoucherDescriptionProps) {
  const { description, termAndConditions } = props

  return (
    <div className="flex w-full flex-col items-start gap-y-xs">
      <Text size="base" state="primary" type="bold">
        Description
      </Text>
      <Text size="sm" state="secondary" type="regular" className="text-left">
        {description}
      </Text>
      {termAndConditions && (
        <>
          <Text size="base" state="primary" type="bold">
            Terms and Condition
          </Text>
          <Text
            size="sm"
            state="secondary"
            type="regular"
            className="text-left"
          >
            {termAndConditions}
          </Text>
        </>
      )}
    </div>
  )
}
