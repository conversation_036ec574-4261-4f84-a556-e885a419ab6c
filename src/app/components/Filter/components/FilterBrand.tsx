import { useCallback, useState } from "react"
import { <PERSON><PERSON><PERSON>, Divider } from "@kickavenue/ui/dist/src/components"

import useFetchBrands from "@app/hooks/useFetchBrands"
import SearchInput from "@components/shared/SearchInput"
import {
  getNewSelectedFilters,
  isFilterSelected,
  mapBrandToOption,
  mapFilterParamsToOption,
} from "@utils/product.utils"
import { Brand as BrandType } from "types/brand.type"
import { TProductFilterKey } from "types/product.type"
import { stringifyFilterOption } from "@components/FilterDashboard/utils"
import useURLQuery from "@app/hooks/useUrlQuery"
import { event } from "@lib/gtag"

import { useBrandUrlSync } from "../hooks/useBrandUrlSync"

import ShowMoreButton from "./ShowMoreButton"
import { FilterHeader } from "./FilterHeader"

const { BrandId, Brand } = TProductFilterKey

const FilterBrand = () => {
  const { brands, brandHasNextPage, brandFetchNextPage, handleBrandSearch } =
    useFetchBrands()

  const { handleBulkChangeQuery } = useURLQuery()
  const [isOpen, setIsOpen] = useState(true)

  // Use the custom hook to handle URL synchronization
  // Provide empty array as default if brands is undefined
  const { searchParams } = useBrandUrlSync(brands || [], handleBulkChangeQuery)

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleBrandSearch(e.target.value)
  }

  const handleCheckboxChange = useCallback(
    (brand: BrandType) => {
      const filterOptions = mapFilterParamsToOption({
        params: searchParams?.toString(),
        labelKey: Brand,
        valueKey: BrandId,
      })
      const brandOption = mapBrandToOption(brand)
      const newSelectedBrands = getNewSelectedFilters(
        filterOptions,
        brandOption,
      )

      handleBulkChangeQuery({
        [BrandId]: stringifyFilterOption("value", newSelectedBrands),
        [Brand]: stringifyFilterOption("label", newSelectedBrands),
      })

      event({
        action: "product_searched",
        params: {
          brand: newSelectedBrands.map((option) => option.label).join(" | "),
        },
      })
    },
    [searchParams, handleBulkChangeQuery],
  )

  const isCheckboxChecked = useCallback(
    (brand: BrandType) =>
      isFilterSelected(
        mapFilterParamsToOption({
          params: searchParams?.toString(),
          labelKey: Brand,
          valueKey: BrandId,
        }),
        mapBrandToOption(brand),
      ),
    [searchParams],
  )

  return (
    <div className="my-4">
      <FilterHeader
        title="Brand"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
      />
      {isOpen && (
        <>
          <SearchInput
            title="Brand"
            onClearText={() => {}}
            onChange={handleSearch}
            className="mb-3"
          />
          {brands?.map((brand) => (
            <div key={brand.id} className="mb-2">
              <CheckBox
                label={brand.name}
                checked={isCheckboxChecked(brand)}
                onChange={() => handleCheckboxChange(brand)}
              />
            </div>
          ))}
          <ShowMoreButton
            showMore={brandHasNextPage}
            onClick={brandFetchNextPage}
          />
        </>
      )}
      <Divider orientation="horizontal" className="!mt-4" />
    </div>
  )
}

export default FilterBrand
