import { Space } from "@kickavenue/ui/components"
import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"

import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import FilterDashboard from "@components/FilterDashboard"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { TConsignmentDashboardTabEnum } from "types/misc.type"

import ConsignmentSellNowModal from "../ConsignmentSellNowModal"
import ConsignmentUpdateListingPriceModal from "../ConsignmentUpdateListingPriceModal"
import ConsignmentTakeOutConfirm from "../ConsignmentTakeOutConfirm"
import ConsignmentTakeOutModal from "../ConsignmentTakeOutModal"

import ConsignmentActiveTable from "./ConsignmentActiveTable"
import ConsignmentActiveBulkActions from "./ConsignmentActiveBulkActions"
import useConsignmentActiveSearchParams from "./hook/useConsignmentActiveSearchParams"
import ConsignmentActiveDetailModal from "./ConsignmentActiveDetailModal"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const { TAB } = ListingItemConstant.FILTER_FIELDS
const { Active } = TConsignmentDashboardTabEnum

const ConsignmentActive = () => {
  const { handleBulkChangeQuery } = useURLQuery()
  const searchParams = useSearchParams()

  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams.get(TAB) || Active

  useConsignmentActiveSearchParams()

  useEffect(() => {
    if (searchParams.get(PAGE) && searchParams.get(PAGE_SIZE)) return
    handleBulkChangeQuery({
      [PAGE]: PAGE_DEFAULT,
      [PAGE_SIZE]: PAGE_SIZE_DEFAULT,
    })
  }, [handleBulkChangeQuery, searchParams])

  const renderFilterDashboard =
    openFilter && selectedTab === Active ? (
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    ) : null

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <Space size="lg" type="margin" direction="y" />
      <ConsignmentActiveTable />
      <ConsignmentActiveBulkActions />
      <ConsignmentActiveDetailModal />
      <ConsignmentUpdateListingPriceModal />
      <ConsignmentTakeOutModal />
      <ConsignmentTakeOutConfirm />
      <ConsignmentSellNowModal />
      {renderFilterDashboard}
    </>
  )
}

export default ConsignmentActive
