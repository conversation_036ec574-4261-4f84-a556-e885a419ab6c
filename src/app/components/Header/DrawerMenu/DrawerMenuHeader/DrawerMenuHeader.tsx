import { IconCloseOutline, Space } from "@kickavenue/ui/dist/src/components"

import styles from "../DrawerMenu.module.css"
import { TDrawerMenuHeaderProps } from "../DrawerMenu.type"

import { ProfileInfo } from "./ProfileInfo"

const DrawerMenuHeader = ({
  toggleDrawerMenu,
  isLogin,
  urlProfile,
  email,
  name,
}: TDrawerMenuHeaderProps) => (
  <>
    <div className="p-base">
      <IconCloseOutline
        onClick={toggleDrawerMenu}
        className={styles.iconClose}
      />
    </div>
    <Space direction="y" size="lg" type="margin" />
    <Space direction="x" size="base" type="padding">
      <ProfileInfo
        isLogin={isLogin}
        urlProfile={urlProfile}
        name={name}
        email={email}
      />
    </Space>
  </>
)

export default DrawerMenuHeader
