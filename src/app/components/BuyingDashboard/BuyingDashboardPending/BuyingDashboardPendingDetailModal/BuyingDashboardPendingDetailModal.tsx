import { useBuyingPendingStore } from "stores/buyingPendingStore"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"

import BuyingDashboardPendingDetailModalContent from "./BuyingDashboardPendingDetailModalContent"

const { BUYING_PENDING_DETAIL } = ModalConstant.MODAL_IDS

const BuyingDashboardPendingDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const { setSelectedRowKeys } = useBuyingPendingStore()

  const handleClose = () => {
    setOpen(false, BUYING_PENDING_DETAIL)
    setSelectedRowKeys([])
  }

  if (!open || modalId !== BUYING_PENDING_DETAIL) return null

  return (
    <Modal
      modalId={BUYING_PENDING_DETAIL}
      className="!min-h-[612px]"
      onClose={handleClose}
    >
      <HeaderModal onClose={handleClose} title="Pending Detail" />
      <BuyingDashboardPendingDetailModalContent />
    </Modal>
  )
}

export default BuyingDashboardPendingDetailModal
