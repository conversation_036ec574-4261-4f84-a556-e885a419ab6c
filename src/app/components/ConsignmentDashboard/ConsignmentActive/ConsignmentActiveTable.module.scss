.container {
  @apply overflow-y-auto max-h-[580px];
  .table-container {
    @apply relative w-full min-h-[calc(80vh-260px)] overflow-x-auto;
    .table {
      @apply w-[907px];
      table-layout: fixed;
      thead {
        tr {
          td {
            @apply bg-gray-w-95 w-fit text-gray-b-65 text-sm font-bold p-sm border-none;
          }
          td:first-child {
            @apply rounded-l-xs;
          }
          td:last-child {
            @apply rounded-r-xs;
          }
        }
      }
      td {
        &:nth-child(n+6):nth-child(-n+9) {
          @apply text-right;
          div {
            @apply justify-end;
          }
        }
      }
      
    }
  }
  @screen md {
    .sticky-header-1,
    .sticky-content-1 {
      @apply sticky left-0 z-10;
    }

    .sticky-header-2,
    .sticky-content-2 {
      @apply sticky left-[48px] z-10;
      &::after {
        content: '';
        @apply absolute top-0 right-0 bottom-0 w-[1px] bg-gray-w-95;
      }
      &::before {
        content: '';
        @apply absolute top-0 -right-[5px] bottom-0 w-[5px] pointer-events-none;
        background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0));
      }
    }

    .sticky-right-header-1,
    .sticky-right-content-1 {
      @apply sticky right-0 z-10;
      &::after {
        content: '';
        @apply absolute top-0 right-0 bottom-0 w-[1px] bg-gray-w-95;
      }
      &::before {
        content: '';
        @apply absolute top-0 -left-[5px] bottom-0 w-[5px] pointer-events-none;
        background: linear-gradient(to left, rgba(0,0,0,0.1), rgba(0,0,0,0));
      }
    }
  }
  .sticky-content-1,
  .sticky-content-2,
  .sticky-right-content-1 {
    @apply bg-white;
  }
}