/* eslint-disable line-comment-position */

import {
  EMenuWizardContentType,
  ERedirectionRuleType,
  IMenuWizardSection,
} from "types/menuWizard"

// Helper function to get current date + days
const getDatePlusDays = (days: number): string => {
  const date = new Date()
  date.setDate(date.getDate() + days)
  return date.toISOString()
}

// Helper function to get current date - days
const getDateMinusDays = (days: number): string => {
  const date = new Date()
  date.setDate(date.getDate() - days)
  return date.toISOString()
}

export const dummyRaffleData: IMenuWizardSection[] = [
  {
    id: 1,
    menuWizardId: 1,
    contentType: EMenuWizardContentType.Raffle,
    sequence: 1,
    backgroundColor: "#000000",
    title: "Air Jordan 1 Retro High OG 'Chicago'",
    description:
      "Win the iconic Air Jordan 1 Chicago colorway in this exclusive raffle",
    actionTitle: "Enter Raffle",
    contentId: 101,
    image:
      "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=800&h=600&fit=crop&auto=format",
    createdAt: getDateMinusDays(7),
    createdBy: "admin",
    updatedAt: getDateMinusDays(1),
    updatedBy: "admin",
    keyword: "jordan-chicago-raffle",
    sectionContent: {
      id: 101,
      name: "Air Jordan 1 Chicago Raffle",
      banner:
        "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=1200&h=400&fit=crop&auto=format",
      startTime: getDateMinusDays(2),
      endTime: getDatePlusDays(5),
      description:
        "Enter for a chance to win the legendary Air Jordan 1 'Chicago' - one of the most coveted sneakers of all time.",
      termsAndConditions:
        "Must be 18+ to enter. One entry per person. Winner will be selected randomly.",
      isActive: true,
      createdAt: getDateMinusDays(7),
      createdBy: "admin",
      updatedAt: getDateMinusDays(1),
      updatedBy: "admin",
      items: [
        {
          id: 1001,
          name: "Air Jordan 1 Retro High OG 'Chicago'",
          skuCode: "AJ1-CHI-2015",
          description:
            "The Air Jordan 1 Retro High OG 'Chicago' brings back the classic colorway",
          colorway: "White/Varsity Red-Black",
          gender: "Unisex",
          underRetail: false,
          retailPrice: {
            amount: "170.00",
            minUnitVal: 17000,
            amountText: "$170.00",
          },
          lowestPrice: {
            amount: "2500.00",
            minUnitVal: 250000,
            amountText: "$2,500.00",
          },
          brand: {
            id: 1,
            name: "Jordan",
            backgroundImage:
              "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop&auto=format",
            logoImage:
              "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop&auto=format",
            description: "Jordan Brand",
            isPartner: true,
            isActive: true,
            createdAt: getDateMinusDays(365),
            createdBy: "system",
            updatedAt: getDateMinusDays(30),
            updatedBy: "system",
          },
          countryId: 1,
          releaseDate: "2015-04-01",
          tag: "OG",
          weight: 500,
          dimension: "35x25x15",
          sizeChartId: 1,
          marketplaceLink:
            "https://stockx.com/air-jordan-1-retro-high-og-chicago-2015",
          isAutoScrape: false,
          commissionFee: 10,
          isActive: true,
          isAddOn: false,
          isVoucherApplicable: true,
          isReceiveSellRequest: true,
          isCanMakeOffer: true,
          isHotItem: true,
          isNonPurchaseable: false,
          images: [
            "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=600&h=600&fit=crop&auto=format",
            "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=600&h=600&fit=crop&auto=format",
          ],
          seoTitleIna: "Air Jordan 1 Chicago Original",
          seoKeywordIna: ["jordan", "chicago", "sneakers", "basketball"],
          seoDescriptionIna: "Sepatu basket legendaris Air Jordan 1 Chicago",
          seoTitleEng: "Air Jordan 1 Retro High OG Chicago",
          seoKeywordEng: [
            "jordan",
            "chicago",
            "sneakers",
            "basketball",
            "retro",
          ],
          seoDescriptionEng:
            "The legendary Air Jordan 1 Chicago basketball sneaker",
          createdAt: getDateMinusDays(30),
          createdBy: "admin",
          updatedAt: getDateMinusDays(1),
          updatedBy: "admin",
        },
      ],
      imagePortrait:
        "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=400&h=600&fit=crop&auto=format",
      imageLandscape:
        "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=800&h=400&fit=crop&auto=format",
      redirectionRule: {
        type: ERedirectionRuleType.Raffle,
        keywords: "jordan-chicago-raffle",
      },
      type: "RAFFLE",
      value: "premium",
      defaultSort: "popularity",
    },
  },
  {
    id: 2,
    menuWizardId: 1,
    contentType: EMenuWizardContentType.Raffle,
    sequence: 2,
    backgroundColor: "#1a1a1a",
    title: "Nike Dunk Low 'Panda'",
    description:
      "Enter to win the popular Nike Dunk Low in the classic Panda colorway",
    actionTitle: "Join Now",
    contentId: 102,
    image:
      "https://images.unsplash.com/photo-**********-b41d501d3772?w=800&h=600&fit=crop&auto=format",
    createdAt: getDateMinusDays(5),
    createdBy: "admin",
    updatedAt: getDateMinusDays(1),
    updatedBy: "admin",
    keyword: "dunk-panda-raffle",
    sectionContent: {
      id: 102,
      name: "Nike Dunk Low Panda Raffle",
      banner:
        "https://images.unsplash.com/photo-**********-b41d501d3772?w=1200&h=400&fit=crop&auto=format",
      startTime: getDateMinusDays(1),
      endTime: getDatePlusDays(3),
      description:
        "Get a chance to own the Nike Dunk Low 'Panda' - the most sought-after colorway of 2023.",
      termsAndConditions:
        "Open to all registered users. Maximum one entry per account.",
      isActive: true,
      createdAt: getDateMinusDays(5),
      createdBy: "admin",
      updatedAt: getDateMinusDays(1),
      updatedBy: "admin",
      items: [
        {
          id: 1002,
          name: "Nike Dunk Low 'Panda'",
          skuCode: "DUNK-PANDA-2021",
          description:
            "The Nike Dunk Low returns with the classic white and black colorway",
          colorway: "White/Black",
          gender: "Unisex",
          underRetail: false,
          retailPrice: {
            amount: "100.00",
            minUnitVal: 10000,
            amountText: "$100.00",
          },
          lowestPrice: {
            amount: "150.00",
            minUnitVal: 15000,
            amountText: "$150.00",
          },
          brand: {
            id: 2,
            name: "Nike",
            backgroundImage:
              "https://images.unsplash.com/photo-*************-0e29a4b5b4aa?w=400&h=300&fit=crop&auto=format",
            logoImage:
              "https://images.unsplash.com/photo-*************-0e29a4b5b4aa?w=100&h=100&fit=crop&auto=format",
            description: "Nike Inc.",
            isPartner: true,
            isActive: true,
            createdAt: getDateMinusDays(365),
            createdBy: "system",
            updatedAt: getDateMinusDays(30),
            updatedBy: "system",
          },
          countryId: 1,
          releaseDate: "2021-03-15",
          tag: "RETRO",
          weight: 450,
          dimension: "33x23x12",
          sizeChartId: 2,
          marketplaceLink: "https://stockx.com/nike-dunk-low-white-black-panda",
          isAutoScrape: false,
          commissionFee: 8,
          isActive: true,
          isAddOn: false,
          isVoucherApplicable: true,
          isReceiveSellRequest: true,
          isCanMakeOffer: true,
          isHotItem: true,
          isNonPurchaseable: false,
          images: [
            "https://images.unsplash.com/photo-**********-b41d501d3772?w=600&h=600&fit=crop&auto=format",
            "https://images.unsplash.com/photo-1460353581641-37baddab0fa2?w=600&h=600&fit=crop&auto=format",
          ],
          seoTitleIna: "Nike Dunk Low Panda Hitam Putih",
          seoKeywordIna: ["nike", "dunk", "panda", "sneakers"],
          seoDescriptionIna:
            "Sepatu Nike Dunk Low dengan warna klasik hitam putih",
          seoTitleEng: "Nike Dunk Low White Black Panda",
          seoKeywordEng: ["nike", "dunk", "panda", "sneakers", "retro"],
          seoDescriptionEng:
            "Classic Nike Dunk Low in the popular Panda colorway",
          createdAt: getDateMinusDays(20),
          createdBy: "admin",
          updatedAt: getDateMinusDays(1),
          updatedBy: "admin",
        },
      ],
      imagePortrait:
        "https://images.unsplash.com/photo-**********-b41d501d3772?w=400&h=600&fit=crop&auto=format",
      imageLandscape:
        "https://images.unsplash.com/photo-**********-b41d501d3772?w=800&h=400&fit=crop&auto=format",
      redirectionRule: {
        type: ERedirectionRuleType.Raffle,
        keywords: "dunk-panda-raffle",
      },
      type: "RAFFLE",
      value: "standard",
      defaultSort: "newest",
    },
  },
]

// Inactive raffle for testing (should not appear in component)
export const inactiveRaffleData: IMenuWizardSection = {
  id: 3,
  menuWizardId: 1,
  contentType: EMenuWizardContentType.Raffle,
  sequence: 3,
  backgroundColor: "#333333",
  title: "Adidas Yeezy Boost 350 V2 'Zebra'",
  description: "This raffle has ended",
  actionTitle: "Ended",
  contentId: 103,
  image:
    "https://images.unsplash.com/photo-1552346154-21d32810aba3?w=800&h=600&fit=crop&auto=format",
  createdAt: getDateMinusDays(10),
  createdBy: "admin",
  updatedAt: getDateMinusDays(5),
  updatedBy: "admin",
  keyword: "yeezy-zebra-raffle",
  sectionContent: {
    id: 103,
    name: "Yeezy Zebra Raffle",
    banner:
      "https://images.unsplash.com/photo-1552346154-21d32810aba3?w=1200&h=400&fit=crop&auto=format",
    startTime: getDateMinusDays(10),
    endTime: getDateMinusDays(2), // Ended 2 days ago
    description: "This raffle has ended.",
    termsAndConditions: "Raffle has concluded.",
    isActive: false, // Inactive
    createdAt: getDateMinusDays(10),
    createdBy: "admin",
    updatedAt: getDateMinusDays(5),
    updatedBy: "admin",
    items: [],
    imagePortrait: "",
    imageLandscape: "",
    redirectionRule: {
      type: ERedirectionRuleType.Raffle,
      keywords: "yeezy-zebra-raffle",
    },
  },
}

// Combined data for testing different scenarios
export const allRaffleTestData: IMenuWizardSection[] = [
  ...dummyRaffleData,
  inactiveRaffleData,
]
