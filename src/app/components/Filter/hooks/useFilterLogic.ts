/* eslint-disable @typescript-eslint/naming-convention */

import { useRouter, useSearchParams } from "next/navigation"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"

import {
  FilterGroupProps,
  isDefaultFilterGroupProps,
  isPriceRangeFilterGroupProps,
  isSizeFilterGroupProps,
} from "../types"

const getParamKey = (title: string): string => {
  const keyMap: Record<string, string> = {
    Brands: "brand",

    "Shipping Method": "shippingMethod",
  }
  return keyMap[title] || title.toLowerCase()
}

const useFilterState = (props: FilterGroupProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [showAll, setShowAll] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [minPrice, setMinPrice] = useState<string>("")
  const [maxPrice, setMaxPrice] = useState<string>("")
  const [currentSizeType, setCurrentSizeType] = useState(
    isSizeFilterGroupProps(props) && props.sizeTypes.length > 0
      ? props.sizeTypes[0]
      : "",
  )

  return {
    isOpen,
    setIsOpen,
    showAll,
    setShowAll,
    searchTerm,
    setSearchTerm,
    selectedItems,
    setSelectedItems,
    minPrice,
    setMinPrice,
    maxPrice,
    setMaxPrice,
    currentSizeType,
    setCurrentSizeType,
  }
}

const useURLParams = (
  title: string,
  setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>,
  setMinPrice: React.Dispatch<React.SetStateAction<string>>,
  setMaxPrice: React.Dispatch<React.SetStateAction<string>>,
  props: FilterGroupProps,
) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const isInitialMount = useRef(true)

  const parseParamsFromURL = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString())
    const paramKey = getParamKey(title)
    const paramValue = params.get(paramKey)

    setSelectedItems(paramValue ? paramValue.split(",") : [])

    if (!isPriceRangeFilterGroupProps(props)) {
      return
    }

    const priceRange = params.get("priceRange")
    if (!priceRange) {
      setMinPrice("")
      setMaxPrice("")
      return
    }

    const [min, max] = priceRange.split("-")
    setMinPrice(min || "")
    setMaxPrice(max || "")
  }, [searchParams, title, props, setSelectedItems, setMinPrice, setMaxPrice])

  useEffect(() => {
    if (!isInitialMount.current) {
      return
    }
    isInitialMount.current = false
    parseParamsFromURL()
  }, [parseParamsFromURL])

  return { router, searchParams, parseParamsFromURL }
}

const useFilterActions = (
  props: FilterGroupProps,
  title: string,
  maxItems: number,
  onFilterChange: ((items: string[]) => void) | undefined,
  state: ReturnType<typeof useFilterState>,
  urlParams: ReturnType<typeof useURLParams>,
) => {
  const { router, searchParams } = urlParams
  const {
    setSelectedItems,
    setMinPrice,
    setMaxPrice,
    setSearchTerm,
    setCurrentSizeType,
  } = state

  const updateParams = useCallback(
    (paramKey: string, newSelectedItems: string[]) => {
      const params = new URLSearchParams(searchParams.toString())
      if (newSelectedItems.length > 0) {
        params.set(paramKey, newSelectedItems.join(","))
      } else {
        params.delete(paramKey)
      }
      router.push(`?${params.toString()}`, { scroll: false })
    },
    [searchParams, router],
  )

  const handleItemClick = useCallback(
    (itemName: string) => {
      setSelectedItems((prev) => {
        const newSelectedItems = prev.includes(itemName)
          ? prev.filter((item) => item !== itemName)
          : [...prev, itemName]

        const paramKey = getParamKey(title)
        updateParams(paramKey, newSelectedItems)
        if (onFilterChange) {
          onFilterChange(newSelectedItems)
        }
        return newSelectedItems
      })
    },
    [title, updateParams, onFilterChange, setSelectedItems],
  )

  const updatePriceRange = useCallback(
    (min: string, max: string) => {
      setMinPrice(min)
      setMaxPrice(max)

      const params = new URLSearchParams(searchParams.toString())
      if (min || max) {
        params.set("priceRange", `${min}-${max}`)
      } else {
        params.delete("priceRange")
      }
      router.push(`?${params.toString()}`, { scroll: false })
    },
    [searchParams, router, setMinPrice, setMaxPrice],
  )

  const handlePriceChange = useCallback(
    (min: string, max: string) => {
      if (!isPriceRangeFilterGroupProps(props)) {
        return
      }
      updatePriceRange(min, max)
      props.onPriceChange(Number(min) || 0, Number(max) || 0)
    },
    [props, updatePriceRange],
  )

  const reset = useCallback(() => {
    setSelectedItems([])
    setMinPrice("")
    setMaxPrice("")
    setSearchTerm("")
    if (isSizeFilterGroupProps(props) && props.sizeTypes.length > 0) {
      setCurrentSizeType(props.sizeTypes[0])
    }
    if (onFilterChange) {
      onFilterChange([])
    }
  }, [
    props,
    onFilterChange,
    setSelectedItems,
    setMinPrice,
    setMaxPrice,
    setSearchTerm,
    setCurrentSizeType,
  ])

  return { handleItemClick, handlePriceChange, reset }
}

export const useFilterLogic = (
  props: FilterGroupProps,
  title: string,
  maxItems: number,
  onFilterChange?: (items: string[]) => void,
) => {
  const state = useFilterState(props)
  const urlParams = useURLParams(
    title,
    state.setSelectedItems,
    state.setMinPrice,
    state.setMaxPrice,
    props,
  )
  const actions = useFilterActions(
    props,
    title,
    maxItems,
    onFilterChange,
    state,
    urlParams,
  )

  const filteredItems = useMemo(() => {
    if (!isDefaultFilterGroupProps(props)) {
      return []
    }
    if (props?.isParentSearch) {
      return props.items
    }
    return props.items.filter((item) =>
      item.name.toLowerCase().includes(state.searchTerm.toLowerCase()),
    )
  }, [props, state.searchTerm])

  const displayedItems = useMemo(
    () =>
      filteredItems.slice(0, state.showAll ? filteredItems.length : maxItems),
    [filteredItems, state.showAll, maxItems],
  )

  const toggleOpen = useCallback(
    () => state.setIsOpen((prev) => !prev),
    [state],
  )
  const toggleShowAll = useCallback(
    () => state.setShowAll((prev) => !prev),
    [state],
  )
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value
      state.setSearchTerm(val)

      if (props?.isParentSearch && props?.onSearch) {
        props.onSearch(val)
      }
    },
    [state, props],
  )

  return {
    ...state,
    ...actions,
    filteredItems,
    displayedItems,
    toggleOpen,
    toggleShowAll,
    handleSearchChange,
    parseParamsFromURL: urlParams.parseParamsFromURL,
  }
}
