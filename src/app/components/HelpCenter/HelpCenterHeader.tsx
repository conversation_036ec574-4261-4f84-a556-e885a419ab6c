import { Text } from "@kickavenue/ui/dist/src/components"
import { Logo } from "@kickavenue/ui/dist/src/components/icons"
import Link from "next/link"

import LanguageSegmented from "@components/shared/LanguageSegmented"

const HelpCenterHeader = () => {
  return (
    <>
      <div className="flex items-center gap-sm">
        <Link href="/">
          <div className="max-w-[184px]">
            <Logo className="h-auto w-full" />
          </div>
        </Link>
        <Text size="base" type="bold" state="primary">
          Help Center
        </Text>
      </div>
      <LanguageSegmented />
    </>
  )
}

export default HelpCenterHeader
