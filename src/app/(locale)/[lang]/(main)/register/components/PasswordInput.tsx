import React from "react"

import { InputPassword } from "@app/components"
import { useRegistrationStore } from "@stores/registrationStore"

const PasswordInput: React.FC = () => {
  const { registrationData, setRegistrationData } = useRegistrationStore()

  return (
    <InputPassword
      value={registrationData.password}
      onChange={(password) => setRegistrationData({ password })}
      confirmationValue={registrationData.passwordConfirmation}
      onConfirmationChange={(confirmation) =>
        setRegistrationData({ passwordConfirmation: confirmation })
      }
      showConfirmation
      showRequirements
      label="Password"
      className="my-4"
    />
  )
}

export default PasswordInput
