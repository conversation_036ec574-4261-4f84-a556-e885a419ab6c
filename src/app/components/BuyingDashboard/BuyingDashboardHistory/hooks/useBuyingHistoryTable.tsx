import moment from "moment"

import ProductDetailColumn from "@components/SellingDashboard/ProductDetailColumn"
import ConditionColumn from "@components/shared/ConditionColumn"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import TxDetailStatus from "@components/shared/TxDetailStatus"
import { MiscConstant } from "@constants/misc"
import { formatPrice } from "@utils/misc"
import { getTxDetailTotalPayment } from "@utils/txDetail.utils"
import { TPackagingConditionEnum, TItemConditionEnum } from "types/listing.type"
import { TTableColumn } from "types/table.type"
import { TTransactionDetail } from "types/transactionDetail.type"

import BuyingDashboardHistoryRowActions from "../BuyingDashboardHistoryRowActions"

const width = {
  orderId: 138,
  productDetails: 248,
  size: 64,
  conditions: 108,
  totalPayment: 148,
  purchaseDate: 140,
  paymentMethod: 140,
  status: 85,
  action: 67,
}

const useBuyingHistoryTable = () => {
  const columns = [
    {
      key: "invoiceNumber",
      title: "Order ID",
      width: width.orderId,
      render: (record: TTransactionDetail) => (
        <>{record.invoiceNumber || "-"}</>
      ),
      sorter: () => {},
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: width.productDetails,
      render: (record: TTransactionDetail) => (
        <ProductDetailColumn listingItem={record.sellerListingDetails} />
      ),
    },
    {
      key: "size",
      title: "Size",
      width: width.size,
      render: (record: TTransactionDetail) => (
        <TitleSubTitleColumn
          title="US"
          subtitle={record?.sellerListingDetails?.size?.us || "-"}
        />
      ),
    },
    {
      key: "itemCondition",
      title: "Conditions",
      width: width.conditions,
      render: (record: TTransactionDetail) => (
        <ConditionColumn
          itemCondition={
            record?.sellerListingDetails?.itemCondition as TItemConditionEnum
          }
          packagingCondition={
            record?.sellerListingDetails
              ?.packagingCondition as TPackagingConditionEnum
          }
        />
      ),
    },
    {
      key: "totalPayment",
      title: "Total Payment",
      width: width.totalPayment,
      render: (record: TTransactionDetail) => (
        <div className="text-left">
          {formatPrice(getTxDetailTotalPayment(record), null, "IDR")}
        </div>
      ),
    },
    {
      key: "createdAt",
      title: "Purchase Date",
      width: width.purchaseDate,
      render: (record: TTransactionDetail) => (
        <>{moment(record.createdAt).format(MiscConstant.DATE_FORMAT)}</>
      ),
      sorter: () => {},
    },
    {
      key: "paymentMethod",
      title: "Payment Method",
      width: width.paymentMethod,
      render: (record: TTransactionDetail) => <>{record.paymentMethodName}</>,
    },
    {
      key: "status",
      title: "Status",
      width: width.status,
      render: (record: TTransactionDetail) => (
        <TxDetailStatus status={record.status} />
      ),
      sorter: () => {},
    },
    {
      key: "action",
      title: "Action",
      width: width.action,
      headerClassName: "[&>div]:justify-center",
      className: "[&>div]:justify-center",
      render: (record: TTransactionDetail) => (
        <BuyingDashboardHistoryRowActions record={record} />
      ),
    },
  ] as TTableColumn[]
  return { columns }
}

export default useBuyingHistoryTable
