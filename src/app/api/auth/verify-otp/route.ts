/* eslint-disable @typescript-eslint/naming-convention */

import { NextRequest, NextResponse } from "next/server"

import { apiHttpClient } from "@infrastructure/providers/apiHttpClient"

export async function POST(request: NextRequest) {
  try {
    const { requests } = await request.json()

    const response = await apiHttpClient.post(
      "/auth/otp/verify",
      {
        requests,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )

    return NextResponse.json(response.data)
  } catch (error: any) {
    return NextResponse.json(
      {
        error: error.response?.data?.Message || "Failed to verify OTP",
        status: error.response?.status || 500,
      },
      { status: error.response?.status || 500 },
    )
  }
}
