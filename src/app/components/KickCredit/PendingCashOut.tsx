"use client"

import { EKickCreditDisbursementType } from "types/disbursement.type"
import DisbursementDataTable from "@components/DisbursementDataTable/DisbursementDataTable"

const PendingCashOut = () => {
  return (
    <DisbursementDataTable
      disbursementType={EKickCreditDisbursementType.KickPendingCashOut}
      emptyStateTitle="No Pending Cash Out"
    />
  )
}

export default PendingCashOut
