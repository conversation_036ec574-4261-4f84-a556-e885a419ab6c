"use client"

import Button from "@kickavenue/ui/dist/src/components/Button/Button"
import IconWhatsappBnw from "@kickavenue/ui/dist/src/components/icons/IconWhatsappBnw"

import IconEmailOutline from "@components/shared/Icons/IconEmailOutline"

const HelpCenterContactAction = () => {
  return (
    <div className="flex gap-sm">
      <Button
        size="md"
        variant="primary"
        className="!w-full"
        IconLeft={IconEmailOutline}
        onClick={() => {
          window.open("mailto:<EMAIL>", "_blank")
        }}
      >
        Email
      </Button>
      <Button
        size="md"
        variant="primary"
        className="!w-full"
        IconLeft={IconWhatsappBnw}
        onClick={() => {
          window.open("https://wa.me/6281210005425", "_blank")
        }}
      >
        Whatsapp
      </Button>
    </div>
  )
}

export default HelpCenterContactAction
