import React from "react"
import { Chip } from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"

import { TListingItemFilterState } from "types/listingItem.type"
import { EBuyingDashboardTab } from "@components/BuyingDashboard/buyingDashboard.type"
import { EConsignmentDashboardTab } from "@components/ConsignmentDashboard/consignmentDashboard.type"
import { ESellingDashboardTab } from "@components/SellingDashboard/sellingDashboard.type"

import { getDashboardTab } from "../utils"

interface PriceRangeChipProps {
  filterState: TListingItemFilterState
  handleRemoveChip: (key: string, item?: string) => void
  formatPrice: (price: string) => string
}

const { Offers } = EBuyingDashboardTab
const { History } = EConsignmentDashboardTab
const { Pending, Active, CgInProgress } = EConsignmentDashboardTab
const { BuyingPending, BuyingInProgress, BuyingHistory } = EBuyingDashboardTab
const { Current, InProgress, SellingHistory } = ESellingDashboardTab

type EDashboardTab = Partial<
  EBuyingDashboardTab | EConsignmentDashboardTab | ESellingDashboardTab
>

const MAPPED_PRICE_MIN_RANGE_CHIP_KEY = {
  // buying
  [Offers]: "offerpriceMin",
  [BuyingPending]: "purchaseamountMin",
  [BuyingInProgress]: "purchaseamountMin",
  [BuyingHistory]: "purchaseamountMin",
  // selling
  [Current]: "listingpriceMin",
  [InProgress]: "soldpriceMin",
  [SellingHistory]: "soldpriceMin",
  // consignment
  [Pending]: "priceMin",
  [Active]: "priceMin",
  [CgInProgress]: "priceMin",
  [History]: "soldpriceMin",
} as Record<EDashboardTab, string>

const MAPPED_PRICE_MAX_RANGE_CHIP_KEY = {
  // buying
  [Offers]: "offerpriceMax",
  [BuyingPending]: "purchaseamountMax",
  [BuyingInProgress]: "purchaseamountMax",
  [BuyingHistory]: "purchaseamountMax",
  // selling
  [Current]: "listingpriceMax",
  [InProgress]: "soldpriceMax",
  [SellingHistory]: "soldpriceMax",
  // consignment
  [Pending]: "priceMax",
  [Active]: "priceMax",
  [CgInProgress]: "priceMax",
  [History]: "soldpriceMax",
} as Record<EDashboardTab, string>

const PriceRangeChip: React.FC<PriceRangeChipProps> = ({
  filterState,
  handleRemoveChip,
  formatPrice,
}) => {
  const searchParams = useSearchParams()
  const dashboardTab = getDashboardTab({ searchParams })

  const minKey = MAPPED_PRICE_MIN_RANGE_CHIP_KEY[dashboardTab as EDashboardTab]
  const maxKey = MAPPED_PRICE_MAX_RANGE_CHIP_KEY[dashboardTab as EDashboardTab]

  const min = filterState[minKey] as string
  const max = filterState[maxKey] as string

  if (min && max) {
    return (
      <Chip
        key="priceRange"
        size="sm"
        isRemovable
        className="!mb-4 whitespace-nowrap"
        onRemove={() => {
          handleRemoveChip(minKey)
          handleRemoveChip(maxKey)
        }}
      >
        {`${formatPrice(min)} - ${formatPrice(max)}`}
      </Chip>
    )
  }
  return null
}

export default PriceRangeChip
