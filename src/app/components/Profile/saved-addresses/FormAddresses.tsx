"use client"

import { Divider, Heading, IconCloseOutline } from "@kickavenue/ui/components"
import React, { useEffect } from "react"
import { useFormState } from "react-dom"
import { useQueryClient } from "@tanstack/react-query"

import Modal from "@app/components/shared/Modal"
import { useModalStore } from "stores/modalStore"
import { useAddressStore } from "stores/addressStore"
import { create } from "@infrastructure/actions/address"
import { TCountry } from "types/country.type"
import { TCity } from "types/city.type"
import { TProvince } from "types/province.type"
import ButtonSave from "@components/shared/Form/ButtonSave"
import useToast from "@app/hooks/useToast"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { ModalConstant } from "@constants/modal"

import FieldFormAddresses from "./FieldFormAddresses"

interface FormAddressesProps {
  countries: TCountry[]
  cities: TCity[]
  provinces: TProvince[]
}

const FormAddresses = (props: FormAddressesProps) => {
  const { countries, cities, provinces } = props
  const { setOpen } = useModalStore()
  const { addresses } = useAddressStore()
  const queryClient = useQueryClient()
  const [isFormValid, setIsFormValid] = React.useState(false)

  const handleCreateAddress = async (prevState: any, formData: FormData) => {
    // If it's the first address or no default address exists, force set as default
    if (addresses.length === 0 || !addresses.some((addr) => addr.isPrimary)) {
      formData.set("is_primary", "on")
    }

    try {
      const resp = await create(prevState, formData)
      if ("errors" in resp) {
        return resp
      }

      await queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_MY_ADDRESS],
      })
      await queryClient.invalidateQueries({
        queryKey: [QueryKeysConstant.GET_ALL_ADDRESS],
      })

      return resp
    } catch (error) {
      return {
        errors: {
          submit: "Failed to create address. Please try again.",
        },
      }
    }
  }

  const [state, formAction] = useFormState(handleCreateAddress, { errors: {} })
  const initialValue = { countryCode: { label: "+62", value: "+62" } }
  const { setShowToast } = useToast()

  useEffect(() => {
    if (!("id" in state)) return
    setOpen(false, "")
    setShowToast(true, "Address successfully added")
  }, [setShowToast, setOpen, state])

  // Determine if we should show the "Set as Default" checkbox
  const showSetAsDefault =
    addresses.length === 0 || !addresses.some((addr) => addr.isPrimary)

  return (
    <>
      <Modal modalId={ModalConstant.MODAL_IDS.NEW_ADDRESS}>
        <div className="flex justify-between p-md">
          <div className="flex grow justify-center text-right">
            <Heading heading="5" textStyle="bold">
              Add New Address
            </Heading>
          </div>
          <IconCloseOutline
            onClick={() => setOpen(false, "")}
            className="scale-150 cursor-pointer"
          />
        </div>
        <Divider orientation="horizontal" />
        <form action={formAction}>
          <div className="max-h-[496px] overflow-scroll">
            <FieldFormAddresses
              state={state}
              countries={countries}
              cities={cities}
              provinces={provinces}
              initialValue={initialValue}
              showSetAsDefault={showSetAsDefault}
              onValidationChange={setIsFormValid}
            />
          </div>
          <div className="p-lg shadow-base">
            <ButtonSave disabled={!isFormValid} />
          </div>
        </form>
      </Modal>
    </>
  )
}

export default FormAddresses
