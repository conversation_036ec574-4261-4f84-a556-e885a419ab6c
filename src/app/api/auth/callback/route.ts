/* eslint-disable @typescript-eslint/naming-convention */

import { NextRequest, NextResponse } from "next/server"

const {
  COGNITO_CLIENT_ID,
  COGNITO_CLIENT_SECRET,
  COGNITO_DOMAIN,
  NEXT_PUBLIC_BASE_URL,
} = process.env

// Disable static generation for this route
export const dynamic = "force-dynamic"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const code = searchParams.get("code")
    // eslint-disable-next-line unused-imports/no-unused-vars
    const state = searchParams.get("state")

    if (!code) {
      return NextResponse.redirect(
        `${NEXT_PUBLIC_BASE_URL}/login?error=no_code`,
      )
    }

    // Token endpoint for Cognito
    const tokenEndpoint = `${COGNITO_DOMAIN}/oauth2/token`

    // Exchange code for tokens
    const tokenResponse = await fetch(tokenEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${COGNITO_CLIENT_ID}:${COGNITO_CLIENT_SECRET}`,
        ).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: COGNITO_CLIENT_ID!,
        code,
        redirect_uri: `${process.env.NEXT_PUBLIC_BASE_URL}/auth/loading`,
      }),
    })

    if (!tokenResponse.ok) {
      // eslint-disable-next-line no-console
      console.error("Token exchange failed:", await tokenResponse.text())
      return NextResponse.redirect(
        `${NEXT_PUBLIC_BASE_URL}/login?error=token_exchange_failed`,
      )
    }

    const tokens = await tokenResponse.json()

    // Create response with redirect
    const response = NextResponse.redirect(`${NEXT_PUBLIC_BASE_URL}/`)

    // Set tokens in HTTP-only cookies
    response.cookies.set("access_token", tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 60 * 60,
    })

    // Set refresh token if available
    if (tokens.refresh_token) {
      response.cookies.set("refresh_token", tokens.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60,
      })
    }

    // Set ID token if available
    if (tokens.id_token) {
      response.cookies.set("id_token", tokens.id_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60,
      })
    }

    return response
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Callback error:", error)
    return NextResponse.redirect(
      `${NEXT_PUBLIC_BASE_URL}/login?error=callback_failed`,
    )
  }
}
