import { cx } from "class-variance-authority"
import Image from "next/image"

import { getProductImageUrl } from "@utils/misc"
import { Product } from "types/product.type"

export interface ProductImageSliderBottomProps {
  selectedIndex: number
  setSelectedIndex: (index: number) => void
  product: Product
  images?: string[]
}

const ProductImageSliderBottom = ({
  selectedIndex,
  setSelectedIndex,
  product,
  images,
}: ProductImageSliderBottomProps) => {
  const selected = (index: number) => index === selectedIndex
  return (
    <div className="flex gap-lg">
      {(images || product?.images || []).map((image, index) => (
        <div
          key={image}
          className={cx(
            selected(index) && "border border-gray-b-65",
            "cursor-pointer bg-gray-w-95 p-2",
          )}
          onKeyDown={() => setSelectedIndex(index)}
          onClick={() => setSelectedIndex(index)}
        >
          <Image
            alt="Product"
            src={getProductImageUrl(image)}
            width={82}
            height={82}
          />
        </div>
      ))}
    </div>
  )
}

export default ProductImageSliderBottom
