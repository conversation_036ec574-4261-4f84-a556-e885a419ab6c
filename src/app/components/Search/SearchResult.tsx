import { useInView } from "react-intersection-observer"
import { useEffect } from "react"

import CenterWrapper from "@shared/CenterWrapper"
import ProductList from "@shared/ProductList"
import { isHttpNotFoundError } from "@utils/network"
import { useSearchStore } from "stores/searchStore"
import SearchLoading from "@components/ExpandedSearch/SearchLoading"
import Spinner from "@components/shared/Spinner/Spinner"
import { useMiscStore } from "stores/miscStore"

import NoProductFound from "./NoProductFound"

export interface SearchResultProps {
  fetchNextPage: () => void
  hasNextPage: boolean
  isFetchingNextPage: boolean
}

const SearchResult = ({
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
}: SearchResultProps) => {
  const {
    products,
    isLoading,
    status,
    error,
    productWishlist,
    setProductWishlist,
  } = useSearchStore()
  const { ref, inView } = useInView()
  const { setShowWishlistSnackbar } = useMiscStore()

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  if (isLoading || status === "pending") {
    return <SearchLoading />
  }

  if (isHttpNotFoundError(error) || !products?.length) {
    return <NoProductFound />
  }

  return (
    <CenterWrapper className="!p-0">
      <ProductList
        list={products}
        containerProps={{ className: "col-span-2 md:col-span-3" }}
        productWishlist={productWishlist}
        onUnWishlisted={(_, product) => setProductWishlist(product.id, 0)}
        onWishlistAdded={(wishlistIds, product) => {
          setProductWishlist(product.id, wishlistIds?.[0] || 0)
          setShowWishlistSnackbar(true)
        }}
      />
      {hasNextPage && (
        <div className="col-span-2 md:col-span-3">
          <div ref={ref} className="py-sm">
            {isFetchingNextPage && <Spinner />}
          </div>
        </div>
      )}
    </CenterWrapper>
  )
}

export default SearchResult
