import {
  IconCloseOutline,
  IconSearchOutline,
  Input,
} from "@kickavenue/ui/components"
import React, { useState } from "react"

export interface SearchVoucherProps {
  onChange?: (value: string) => void
}

const SearchVoucher = (props: SearchVoucherProps) => {
  const { onChange = () => {} } = props
  const [search, setSearch] = useState<string>("")
  const [placeholder, setPlaceholder] = useState<string | undefined>("Search")
  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    onChange(value)
    setSearch(value)
  }

  const handleReset = () => {
    setSearch("")
    onChange("")
  }

  const showResetIcon = search.length > 0 && (
    <IconCloseOutline onClick={handleReset} className="cursor-pointer" />
  )

  return (
    <Input
      leftIcon={<IconSearchOutline />}
      rightIcon={showResetIcon}
      onChange={handleOnChange}
      placeholder={placeholder}
      size="sm"
      value={search}
      className="rounded-sm bg-gray-w-95"
      onFocus={() => setPlaceholder(undefined)}
      onBlur={() => setPlaceholder("Search")}
    />
  )
}

export default SearchVoucher
