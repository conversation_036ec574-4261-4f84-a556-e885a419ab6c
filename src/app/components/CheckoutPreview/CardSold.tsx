import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  IconArrowRightOutline,
  IconDangerCircleBulk,
  IconSuccessCircleBulk,
  Space,
  Text,
} from "@kickavenue/ui/components"
import React from "react"
import Link from "next/link"

import { formatCurrency } from "@utils/separator"
import { FormStateOffer } from "stores/createOfferStore"

import ProductDetailPreview from "./ProductDetailPreview"

interface CardSoldProps extends Pick<FormStateOffer, "amount"> {
  isError: boolean
  isSuccess: boolean
}
const CardSold = (props: CardSoldProps) => {
  const { amount, isError, isSuccess } = props
  return (
    <div className="rounded-sm bg-white lg:w-[612px]">
      <div className="flex flex-col items-center pt-lg">
        {isError && (
          <>
            <IconDangerCircleBulk className="size-16 text-danger" />
            <Space size="lg" type="margin" direction="y" />
            <Heading heading="4" textStyle="bold">
              Failed!
            </Heading>
            <Space size="base" type="margin" direction="y" />
            <Text size="base" state="secondary" type="regular">
              We&apos;re sorry, but your offer could not be placed.
            </Text>
          </>
        )}
        {isSuccess && (
          <>
            <IconSuccessCircleBulk className="size-16 text-success" />
            <Space size="lg" type="margin" direction="y" />
            <Heading heading="4" textStyle="bold">
              Success!
            </Heading>
            <Space size="base" type="margin" direction="y" />
            <Text size="base" state="secondary" type="regular">
              Your offer has been successfully placed!
            </Text>
          </>
        )}
        <Text size="base" state="primary" type="bold" className="mt-xs">
          {formatCurrency(amount || 0, ",", "IDR")}
        </Text>
      </div>
      <Space size="lg" type="margin" direction="y" />
      <div className="mx-lg rounded-xl border border-solid border-gray-w-80 p-sm">
        <ProductDetailPreview />
      </div>
      <Space size="md" type="margin" direction="y" />
      <Divider orientation="horizontal" />
      <div className="flex justify-between gap-x-xs p-lg">
        <Link href="/" className="w-full">
          <Button variant="secondary" className="!w-full">
            Back to Home Page
          </Button>
        </Link>
        <Link href="/profile/buying" className="w-full">
          <Button IconRight={IconArrowRightOutline} className="!w-full">
            See My Offers
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default CardSold
