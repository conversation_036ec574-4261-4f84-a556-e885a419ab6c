import { useEffect } from "react"
import { useSearchParams } from "next/navigation"

import { ConsignmentDashboardTabEnum } from "types/listingItem.type"
import { ListingItemConstant } from "@constants/listingitem.constant"
import { useConsignmentInProgressStore } from "stores/consignmentInProgressStore"
import {
  TSellerStatus,
  TTransactionDetailFilter,
} from "types/transactionDetail.type"
import { MiscConstant } from "@constants/misc"
import { getFilterFromQuery } from "@utils/query.utils"
import { formatDate } from "@utils/date.utils"
import { getConditionFilter } from "@utils/listingItem"
import { stringToNumberArray } from "@utils/string.utils"

const {
  PAGE,
  PAGE_SIZE,
  TAB,
  SEARCH,
  SOLD_PRICE_MIN,
  SOLD_PRICE_MAX,
  SOLD_DATE_START,
  SOLD_DATE_END,
  CONDITION,
  CATEGORY_ID,
  BRAND_ID,
  SIZE_ID,
  SORT_BY,
} = ListingItemConstant.FILTER_FIELDS
const { PAGE: PAGE_DEFAULT, PAGE_SIZE: PAGE_SIZE_DEFAULT } =
  MiscConstant.PAGING_DEFAULT

const {
  WaitingSellerConfirmation,
  WaitingSellerDelivering,
  EnRouteToKickAvenue,
  AuthenticationProcess,
  VerificationFailed,
  Waiting99PercentPerfectPhotos,
  WaitingCompensationAmount,
  WaitingBuyerDecision,
  WaitingReturnCourierAssignment,
  WaitingReturnAwbNumber,
  EnRouteToSeller,
  ReturnedWaitingRackAssignment,
  Consigned,
} = TSellerStatus

const useConsignmentInProgressSearchParams = () => {
  const searchParams = useSearchParams()
  const { setFilter } = useConsignmentInProgressStore()

  useEffect(() => {
    const tab = searchParams.get(TAB)
    if (
      tab !== ConsignmentDashboardTabEnum.CgInProgress ||
      !searchParams.toString()
    ) {
      return
    }
    const page = searchParams.get(PAGE)
    const pageSize = searchParams.get(PAGE_SIZE)
    const search = searchParams.get(SEARCH)
    const soldPriceMin = searchParams.get(SOLD_PRICE_MIN)
    const soldPriceMax = searchParams.get(SOLD_PRICE_MAX)
    const soldDateStart = searchParams.get(SOLD_DATE_START)
    const soldDateEnd = searchParams.get(SOLD_DATE_END)
    const condition = searchParams.get(CONDITION)
    const categoryId = searchParams.get(CATEGORY_ID)
    const brandId = searchParams.get(BRAND_ID)
    const sizeId = searchParams.get(SIZE_ID)
    const sortBy = searchParams.get(SORT_BY)

    const filter = {
      page: page ? Number(page) : PAGE_DEFAULT,
      pageSize: pageSize ? Number(pageSize) : PAGE_SIZE_DEFAULT,
      sort: [],
      search,
      isConsignment: true,
      sellerStatus: [
        WaitingSellerConfirmation,
        WaitingSellerDelivering,
        EnRouteToKickAvenue,
        AuthenticationProcess,
        VerificationFailed,
        Waiting99PercentPerfectPhotos,
        WaitingCompensationAmount,
        WaitingBuyerDecision,
        WaitingReturnCourierAssignment,
        WaitingReturnAwbNumber,
        EnRouteToSeller,
        ReturnedWaitingRackAssignment,
        Consigned,
      ],
      startPurchaseAmount: getFilterFromQuery(soldPriceMin),
      endPurchaseAmount: getFilterFromQuery(soldPriceMax),
      startDate: formatDate({
        date: soldDateStart,
        format: MiscConstant.DATE_FORMAT_ISO,
        fallback: null,
      }),
      endDate: formatDate({
        date: soldDateEnd,
        format: MiscConstant.DATE_FORMAT_ISO,
        fallback: null,
      }),
      sizeId: stringToNumberArray(sizeId),
      categoryId: stringToNumberArray(categoryId),
      brandId: stringToNumberArray(brandId),
      sortBy,
      ...getConditionFilter(condition),
    } as TTransactionDetailFilter
    setFilter(filter)
  }, [searchParams, setFilter])
}

export default useConsignmentInProgressSearchParams
