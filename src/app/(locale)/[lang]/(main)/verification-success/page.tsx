"use client"

import React from "react"
import {
  But<PERSON>,
  Heading,
  IconSuccessCircleBulk,
  Text,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"

const VerificationSuccessPage = () => {
  const router = useRouter()

  const handleStartExploring = () => {
    router.push("/login")
  }

  return (
    <div className="flex h-[80vh] flex-col items-center justify-center px-4">
      <div className="flex max-w-md flex-col items-center text-center">
        <IconSuccessCircleBulk className="min-h-16 min-w-16 text-green" />

        <Heading as="h4" heading="4" textStyle="bold" className="mt-8">
          Welcome to the Family
        </Heading>

        <Text size="base" state="secondary" type="regular" className="mt-3">
          Remember to stay authentic!
        </Text>

        <Button onClick={handleStartExploring} className="mt-8 w-full max-w-xs">
          Start Exploring
        </Button>
      </div>
    </div>
  )
}

export default VerificationSuccessPage
