import { IconSuccessCircleBulk, Text } from "@kickavenue/ui/components"

import { InputVariant } from "@domain/entities/Input"
import {
  PasswordRequirement,
  PasswordValidationResult,
} from "@domain/entities/PasswordTypes"

export const getInputVariant = (
  isDirty: boolean,
  isConfirmation: boolean,
  validationResult: PasswordValidationResult,
  value: string,
  confirmationValue?: string,
  showRequirements?: boolean,
): InputVariant["variant"] => {
  if (isConfirmation) {
    if (!isDirty || !confirmationValue) return undefined
    return value === confirmationValue ? "success" : "danger"
  }
  if (!isDirty) return undefined
  if (validationResult.isValid) return "success"
  if (!showRequirements && validationResult.error) return "danger"
  return undefined
}

export const getHelperText = (
  isConfirmation: boolean,
  isDirty: boolean,
  validationResult: PasswordValidationResult,
  value: string,
  confirmationValue?: string,
  showRequirements?: boolean,
): string | undefined => {
  if (isConfirmation) {
    if (isDirty && confirmationValue) {
      return value === confirmationValue
        ? "Passwords match"
        : "Passwords do not match"
    }
    return undefined
  }
  if (!showRequirements && validationResult.error) {
    return validationResult.error
  }
  return undefined
}

export const renderRequirements = (
  validationResult: PasswordValidationResult,
) => {
  const stateReq = (req: PasswordRequirement) =>
    req.met ? "success" : "secondary"

  return (
    <div className="mt-2 grid grid-cols-2 gap-2">
      {validationResult.requirements.map((req) => (
        <Text
          key={req.label}
          size="xs"
          type="regular"
          state={stateReq(req)}
          className="col-span-1 flex !w-full items-center gap-1"
        >
          <IconSuccessCircleBulk className="mr-1 !h-3 !w-3 shrink-0" />{" "}
          {req.label}
        </Text>
      ))}
    </div>
  )
}
