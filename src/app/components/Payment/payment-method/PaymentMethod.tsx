import useFetchMyBalance from "@app/hooks/useFetchMyBalance"
import { usePaymentStore } from "stores/paymentStore"
import { EPaymentMethodType } from "types/paymentMethod.type"
import { getPaymentMethodByType } from "@utils/paymentMethod.utils"

import PaymentCreditBalance from "./PaymentCreditBalance"
import PaymentKickPoints from "./PaymentKickPoints"

const { EWallet, FullWallet } = EPaymentMethodType

export default function PaymentMethod() {
  const { listPaymentMethod, setUserBalance } = usePaymentStore()

  const walletPayments = getPaymentMethodByType(listPaymentMethod, [
    EWallet,
    FullWallet,
  ])

  useFetchMyBalance({
    enabled: walletPayments.length > 0,
    onSuccess: (balance) => {
      setUserBalance(balance)
    },
  })

  return (
    <div className="flex flex-col gap-sm">
      <PaymentCreditBalance />
      <PaymentKickPoints />
    </div>
  )
}
