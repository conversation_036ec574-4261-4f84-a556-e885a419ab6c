import { useQuery } from "@tanstack/react-query"

import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { MenuWizardApiRepository } from "@infrastructure/repositories/menuWizardApiRepository"
import { EMenuWizardPageType, IMenuWizard } from "types/menuWizard"

const useMenuWizard = (pageType: EMenuWizardPageType) => {
  const fetchMenuWizard = async () => {
    const menuWizard = new MenuWizardApiRepository()
    const res = await menuWizard.getCurrent(pageType)
    return res.data as IMenuWizard
  }

  return useQuery({
    queryKey: [QueryKeysConstant.GET_MENU_WIZARD, pageType],
    queryFn: fetchMenuWizard,
    enabled: Boolean(pageType),
  })
}

export default useMenuWizard
