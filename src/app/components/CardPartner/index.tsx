import Image from "next/image"
import { But<PERSON>, Heading } from "@kickavenue/ui/components"

const CardPartner: React.FC = () => {
  return (
    <div className="w-full">
      <div className="relative !h-[360px] w-full">
        <Image
          src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          alt="image"
          fill
          className="size-full rounded-t-xl object-cover"
        />
      </div>
      <div className="flex w-full items-center justify-between rounded-b-xl bg-black px-4 py-3">
        <div className="!w-4/5">
          <Heading heading="5" textStyle="bold" className="truncate text-white">
            Partner
          </Heading>
        </div>
        <div className="w-full">
          <Button size="md" variant="secondary" className="!max-w-[54px]">
            View
          </Button>
        </div>
      </div>
    </div>
  )
}

export default CardPartner
