import { TTransaction } from "types/transaction.type"
import PaymentSummary from "@components/Payment/payment-summary/PaymentSummary"
import useFetchInfiniteMyVoucher from "@app/hooks/useFetchMyVoucher"
import { isEmpty } from "@utils/misc"
import { TTransactionDetail } from "types/transactionDetail.type"

import usePaymentStatusSummary from "../hooks/usePaymentStatusSummary"

const PaymentStatusSummaryModalContent = ({
  transaction,
  isOpen,
  txDetails,
}: {
  transaction?: TTransaction | null
  txDetails?: TTransactionDetail[] | null
  isOpen: boolean
}) => {
  const shouldFetchVoucher = isOpen && !isEmpty(transaction?.voucherCode)
  const qVoucher = useFetchInfiniteMyVoucher(shouldFetchVoucher, {
    code: transaction?.voucherCode,
  })
  const voucher = qVoucher.data?.[0]

  const { summaryItems, totalPaymentText } = usePaymentStatusSummary({
    txDetails,
    transaction,
    voucher,
  })

  return (
    <div className="!h-[249px] min-w-[calc(100vw-40px)] p-lg md:w-full md:min-w-0">
      <PaymentSummary
        totalPaymentText={totalPaymentText}
        itemPrices={summaryItems}
      />
    </div>
  )
}

export default PaymentStatusSummaryModalContent
