"use client"

import { useEffect } from "react"
import { notFound } from "next/navigation"
import { Breadcrumb, Divider, Text } from "@kickavenue/ui/dist/src/components"
import NavigationOption from "@kickavenue/ui/dist/src/components/NavigationOption"

import useFetchCurrentFaq from "@components/HelpCenter/hook/useFetchCurrentFaq"
import SpinnerLoading from "@components/shared/SpinnerLoading"
import {
  buildBreadcrumbItem,
  getFaqContentHovered,
  getFaqContentWeight,
} from "@utils/faq.utils"
import useChangePage from "@app/hooks/useChangePage"
import { TFaq, TFaqContent } from "types/faq.type"

import HelpCenterSection from "../HelpCenterSection"
import HelpCenterArticle from "../HelpCenterArticle"
import HelpCenterSearchInput from "../HelpCenterSearch/HelpCenterSearchInput"

interface HelpCenterContentProps {
  category: string
  faqId: string
}

const HelpCenterContent = ({ category, faqId }: HelpCenterContentProps) => {
  const { mappedData, isLoading } = useFetchCurrentFaq()
  const { goToPage } = useChangePage()
  useEffect(() => {
    if (mappedData?.[category]?.contentsMapped?.[faqId] || isLoading) {
      return
    }
    notFound()
  }, [mappedData, category, faqId, isLoading])
  if (isLoading) {
    return <SpinnerLoading />
  }
  const faq = mappedData?.[category] as TFaq
  const faqContent = mappedData?.[category]?.contentsMapped?.[
    faqId
  ] as TFaqContent
  return (
    <HelpCenterSection parentClassName="mt-xl px-base pb-xl xl:px-0 xl:min-h-[calc(100vh-300px)]">
      <div className="mb-xl flex flex-wrap justify-between gap-base">
        <div className="max-w-md overflow-x-auto md:max-w-full">
          <Breadcrumb
            listItem={buildBreadcrumbItem({ mappedData, category, faqId })}
            className="whitespace-nowrap"
          />
        </div>
        <HelpCenterSearchInput
          onClearText={() => {}}
          className="max-w-[294px]"
          placeholder="Search"
          size="sm"
        />
      </div>
      <div className="flex gap-lg">
        <div className="flex max-w-[294px] flex-col gap-base lg:w-[294px]">
          <Text size="base" type="bold" state="primary">
            {faq?.category}
          </Text>
          <div className="">
            {faq?.contents?.map((content) => (
              <NavigationOption
                key={content.id}
                title={content.title}
                className="cursor-pointer"
                weight={getFaqContentWeight(content, faqId)}
                selected={getFaqContentHovered(content, faqId)}
                onClick={() =>
                  goToPage(`/help-center/${category}/${content.id}`)
                }
              />
            ))}
          </div>
        </div>
        <div className="flex flex-col gap-lg lg:flex-1">
          <Text size="base" type="regular" state="secondary">
            {faqContent?.content}
          </Text>
          <Divider orientation="horizontal" />
          <HelpCenterArticle />
        </div>
      </div>
    </HelpCenterSection>
  )
}

export default HelpCenterContent
