import { useCallback, useState } from "react"
import { useSearchParams } from "next/navigation"
import { CheckB<PERSON>, Divider } from "@kickavenue/ui/dist/src/components"

import {
  getNewSelectedFilters,
  isFilterSelected,
  mapCategoryToOption,
  mapFilterParamsToOption,
} from "@utils/product.utils"
import useFetchCategories from "@app/hooks/useFetchCategories"
import useUrlQuery from "@app/hooks/useUrlQuery"
import { TCategory } from "types/category.type"
import { TProductFilterKey } from "types/product.type"
import { stringifyFilterOption } from "@components/FilterDashboard/utils"
import { event } from "@lib/gtag"

import { FilterHeader } from "./FilterHeader"
import ShowMoreButton from "./ShowMoreButton"

const { SubCategoryId, SubCategory } = TProductFilterKey

const FilterSubcategory = ({
  cachedCategories,
}: {
  cachedCategories?: TCategory[]
}) => {
  const { categories } = useFetchCategories({
    enabled: !cachedCategories?.length,
  })
  const subCategories = categories?.filter((cat) => cat.parentId) || []

  const [isOpen, setIsOpen] = useState(false)
  const { handleBulkChangeQuery } = useUrlQuery()
  const searchParams = useSearchParams()
  const [showMore, setShowMore] = useState(false)

  const handleCheckboxChange = useCallback(
    (category: TCategory) => {
      const filterOptions = mapFilterParamsToOption({
        params: searchParams?.toString(),
        labelKey: SubCategory,
        valueKey: SubCategoryId,
      })
      const categoryOption = mapCategoryToOption(category)

      const newSelectedCategories = getNewSelectedFilters(
        filterOptions,
        categoryOption,
      )
      handleBulkChangeQuery({
        [SubCategoryId]: stringifyFilterOption("value", newSelectedCategories),
      })

      event({
        action: "product_searched",
        params: {
          subcategory: newSelectedCategories.map((c) => c.label).join(" | "),
        },
      })
    },
    [searchParams, handleBulkChangeQuery],
  )

  const isCheckboxChecked = useCallback(
    (category: TCategory) =>
      isFilterSelected(
        mapFilterParamsToOption({
          params: searchParams?.toString(),
          labelKey: SubCategoryId,
          valueKey: SubCategoryId,
        }),
        mapCategoryToOption(category),
      ),
    [searchParams],
  )

  return (
    <div className="my-4">
      <FilterHeader
        title="Subcategory"
        isOpen={isOpen}
        toggleOpen={() => setIsOpen(!isOpen)}
      />
      {isOpen && (
        <>
          {subCategories
            ?.slice(0, showMore ? undefined : 10)
            ?.map((category) => (
              <div key={category.id} className="mb-2">
                <CheckBox
                  checked={isCheckboxChecked(category)}
                  onChange={() => handleCheckboxChange(category)}
                  label={category.name as string}
                  className="!mb-3"
                />
              </div>
            ))}
          {!showMore && subCategories?.length > 10 && (
            <ShowMoreButton
              showMore={!showMore}
              onClick={() => setShowMore(true)}
            />
          )}
        </>
      )}
      <Divider orientation="horizontal" className="!mt-4" />
    </div>
  )
}

export default FilterSubcategory
