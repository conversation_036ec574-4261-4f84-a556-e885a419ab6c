import { Space } from "@kickavenue/ui/dist/src/components"

import { ModalConstant } from "@constants/modal"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"
import UpdatePriceOptions from "@components/shared/UpdatePriceOptions"
import { useModalStore } from "stores/modalStore"
import UpdatePriceInput from "@components/shared/UpdatePriceInput"
import UpdatePriceAlert from "@components/shared/UpdatePriceAlert"
import ModalCancelConfirm from "@components/shared/ModalCancelConfirm"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import useBulkUpdateOfferPrice from "@app/hooks/useBulkUpdateOfferPrice"

import useBuyingDashboardOfferUpdatePriceOptions from "../hooks/useBuyingDashboardOfferUpdatePriceOptions"

import BuyingDashboardOfferUpdatePriceTable from "./BuyingDashboardOfferUpdatePriceTable"

const { UPDATE_OFFER_PRICE } = ModalConstant.MODAL_IDS

const BuyingDashboardOfferUpdatePriceModal = () => {
  const { setOpen, open, modalId } = useModalStore()
  const {
    setUpdatePrice,
    setUpdatePriceAction,
    updatePrice,
    updatePriceAction,
    selectedRowKeys,
  } = useBuyingOfferStore()

  const { options, handleOptionSelect } =
    useBuyingDashboardOfferUpdatePriceOptions()

  const { handleBulkUpdatePrice, isPending } = useBulkUpdateOfferPrice()

  const disabledButton =
    Math.abs((updatePrice || 0) % 10000) !== 0 ||
    !updatePrice ||
    isPending ||
    String(updatePrice) === "-"

  const handleClearState = () => {
    setUpdatePrice(null)
    setUpdatePriceAction("")
  }

  const handleClose = () => {
    setOpen(false, UPDATE_OFFER_PRICE)
    handleClearState()
  }

  if (!open || modalId !== UPDATE_OFFER_PRICE) {
    return null
  }

  return (
    <Modal modalId={UPDATE_OFFER_PRICE} className="sm:!max-w-[1036px]">
      <HeaderModal onClose={handleClose} title="Update Offer Price" />
      <Space size="md" direction="y" type="margin" />
      <div className="h-[667px] overflow-y-auto">
        <UpdatePriceOptions
          options={options}
          handleOptionSelect={handleOptionSelect}
          renderUpdatePriceInput={() => (
            <UpdatePriceInput
              setUpdatePrice={setUpdatePrice}
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
            />
          )}
          renderUpdatePriceAlert={() => (
            <UpdatePriceAlert
              updatePriceAction={updatePriceAction}
              updatePrice={updatePrice}
              selectedRowKeys={selectedRowKeys}
            />
          )}
        />
        <BuyingDashboardOfferUpdatePriceTable />
      </div>
      <ModalCancelConfirm
        disableConfirm={disabledButton}
        onConfirm={handleBulkUpdatePrice}
        onCancel={handleClose}
      />
    </Modal>
  )
}

export default BuyingDashboardOfferUpdatePriceModal
