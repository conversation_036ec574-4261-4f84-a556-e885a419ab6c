/* eslint-disable @typescript-eslint/naming-convention */

import { NextRequest, NextResponse } from "next/server"

import { apiHttpClient } from "@infrastructure/providers/apiHttpClient"

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json()

    const response = await apiHttpClient.post(
      "/auth/create",
      {
        country_id: userData.countryId,
        created_source: userData.createdSource,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        password: userData.password,
        password_confirmation: userData.passwordConfirmation,
        phone_number: userData.phoneNumber,
        token_activation: userData.tokenActivation,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )

    return NextResponse.json(response.data)
  } catch (error: any) {
    return NextResponse.json(
      {
        error: error.response?.data?.Message || "Registration failed",
        status: error.response?.status || 500,
      },
      { status: error.response?.status || 500 },
    )
  }
}
