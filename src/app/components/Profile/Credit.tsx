import React, { ReactNode, KeyboardEvent } from "react"
import { IconEyeSlashOutline } from "@kickavenue/ui/components/icons"
import { useRouter } from "next/navigation"

import styles from "./Credit.module.scss"

export interface CreditProps {
  title: string
  icon: ReactNode
  credit: string
  route: string
}

const Credit = (props: CreditProps) => {
  const { title, credit, icon, route } = props
  const router = useRouter()

  const handleInteraction = () => {
    router.push(`/profile/${route}`)
  }

  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
    if (event.key === "Enter" || event.key === " ") {
      handleInteraction()
    }
  }

  return (
    <div
      className={`${styles.credit} cursor-pointer`}
      onClick={handleInteraction}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
    >
      <div className="flex justify-between">
        {icon}
        <IconEyeSlashOutline className="text-gray-w-40" />
      </div>
      <span className="my-xxs font-normal">{title}</span>
      <span className="font-bold">{credit}</span>
    </div>
  )
}

export default Credit
