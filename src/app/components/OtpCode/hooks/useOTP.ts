import { useState, useCallback } from "react"

const focusNextInput = (index: number, type: "phone" | "email") => {
  const nextInput = document.querySelector(
    `input[name=${type}-otp-${index + 1}]`,
  ) as HTMLInputElement
  nextInput?.focus()
}

const focusPreviousInput = (index: number, type: "phone" | "email") => {
  const prevInput = document.querySelector(
    `input[name=${type}-otp-${index - 1}]`,
  ) as HTMLInputElement
  prevInput?.focus()
}

const useOTP = (type: "phone" | "email") => {
  const [otpValues, setOtpValues] = useState<string[]>(Array(6).fill(""))
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = useCallback(
    (value: string, index: number) => {
      if (isNaN(Number(value))) {
        return
      }

      setOtpValues((prevValues) => {
        const newValues = [...prevValues]
        newValues[index] = value
        return newValues
      })

      if (value && index < 5) {
        focusNextInput(index, type)
      }
    },
    [type],
  )

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
      if (e.key !== "Backspace") return

      e.preventDefault()

      if (otpValues[index] === "" && index > 0) {
        setOtpValues((prevValues) => {
          const newValues = [...prevValues]
          newValues[index - 1] = ""
          return newValues
        })
        focusPreviousInput(index, type)
        return
      }

      setOtpValues((prevValues) => {
        const newValues = [...prevValues]
        newValues[index] = ""
        return newValues
      })
    },
    [otpValues, type],
  )

  const handlePaste = useCallback(
    (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault()
      const pastedData = e.clipboardData.getData("text").slice(0, 6).split("")
      setOtpValues((prevValues) => {
        const newValues = [...prevValues]
        pastedData.forEach((value, index) => {
          if (index < 6) newValues[index] = value
        })
        return newValues
      })

      const nextEmptyIndex = pastedData.findIndex(
        (val, index) => index >= pastedData.length || val === "",
      )
      const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex
      focusNextInput(focusIndex - 1, type)
    },
    [type],
  )

  return {
    otpValues,
    error,
    setError,
    handleInputChange,
    handleKeyDown,
    handlePaste,
  }
}

export default useOTP
