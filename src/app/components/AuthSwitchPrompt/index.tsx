import { Button, Text } from "@kickavenue/ui/components"
import Link from "next/link"
import React from "react"

interface AuthSwitchPromptProps {
  promptText: string
  linkText: string
  linkHref: string
}

const AuthSwitchPrompt: React.FC<AuthSwitchPromptProps> = ({
  promptText,
  linkText,
  linkHref,
}) => {
  return (
    <div className="mb-8 mt-lg flex justify-center text-sm">
      <Text size="sm" type="regular" state="secondary">
        {promptText}
      </Text>
      <Button size="md" variant="link" className="ml-1 !p-0">
        <Link href={linkHref}>{linkText}</Link>
      </Button>
    </div>
  )
}

export default AuthSwitchPrompt
