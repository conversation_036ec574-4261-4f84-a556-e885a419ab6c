import { TTransactionDetailStatus } from "types/transactionDetail.type"

const { WaitingForPayment } = TTransactionDetailStatus

export function getBuyingPendingDefaultStatus() {
  return [WaitingForPayment]
}

export function stripInvalidStatus(
  status?: TTransactionDetailStatus[] | null,
): TTransactionDetailStatus[] {
  if (!status) return []
  return status.filter((s) => getBuyingPendingDefaultStatus().includes(s))
}
