"use client"

import {
  IconLocationSuccessBulkColor,
  Text,
  Divider,
  Badge,
} from "@kickavenue/ui/components"
import { useEffect, useState } from "react"
import { cx } from "class-variance-authority"

import { TAddress } from "types/address.type"
import { getMemberFullName } from "@utils/member.utils"
import { getLocation } from "@utils/location.utils"
import { useLocationStore } from "stores/locationStore"
import { useMemberStore } from "stores/memberStore"

import DropdownAddress from "./DropdownAddress"
import { useFetchAddress } from "./hooks/useFetchAddress"

export interface CardAddressesProps {
  defaultAddress?: TAddress
  onItemClick?: (item: TAddress) => void
  isClickable?: boolean
}

const CardAddresses = (props: CardAddressesProps) => {
  const { defaultAddress, isClickable = false, onItemClick = () => {} } = props
  const { member } = useMemberStore()
  const { cities, provinces, countries } = useLocationStore()
  const [selectedItem, setSelectedItem] = useState<number | undefined>(
    defaultAddress?.id,
  )

  const handleItemClick = (item: TAddress) => {
    if (!isClickable) return
    onItemClick(item)
    setSelectedItem(item.id)
  }

  useEffect(() => {
    if (defaultAddress) {
      setSelectedItem(defaultAddress.id)
    }
  }, [defaultAddress])

  const { addresses, isSuccess, scrollContainerRef } = useFetchAddress()

  return (
    <div className="h-full" ref={scrollContainerRef}>
      {isSuccess &&
        addresses?.map((item) => {
          const isSelected = () => {
            if (!isClickable) return "border-gray-w-80 bg-white"
            if (item.id === selectedItem) {
              return "border-gray-b-65 bg-gray-w-90"
            } else {
              return "border-gray-w-80 bg-white"
            }
          }
          return (
            <button
              key={item.id}
              className={cx(
                "mt-base w-full cursor-default rounded-xl border border-solid text-start",
                isSelected(),
                isClickable ? "cursor-pointer" : "cursor-default",
              )}
              onClick={() => {
                if (isClickable) {
                  handleItemClick(item)
                }
              }}
              type="button"
            >
              <div className="relative flex items-center gap-x-xs p-sm">
                <IconLocationSuccessBulkColor className="size-5 scale-100" />
                <Text size="sm" state="primary" type="bold">
                  {item.title}
                </Text>
                <div className="size-1 rounded-full bg-black" />
                <Text size="sm" state="primary" type="bold">
                  {getMemberFullName(member)}
                </Text>
                <div className="grow">
                  {item.isPrimary && (
                    <Badge size="md" text="Default" type="information" />
                  )}
                </div>
                <DropdownAddress address={item} />
              </div>
              <Divider orientation="horizontal" />
              <div className="p-sm">
                <Text size="sm" state="secondary" type="regular">
                  {item.phoneNumber}
                </Text>
                <Text size="sm" state="secondary" type="regular">
                  {item.addressDetail}
                </Text>
                <Text size="sm" state="secondary" type="regular">
                  {getLocation({
                    address: item,
                    cities,
                    provinces,
                    countries,
                  })}
                </Text>
              </div>
            </button>
          )
        })}
    </div>
  )
}

export default CardAddresses
