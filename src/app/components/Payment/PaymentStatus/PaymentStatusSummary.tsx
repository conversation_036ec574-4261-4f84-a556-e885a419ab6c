import { Button } from "@kickavenue/ui/components/Button"
import Divider from "@kickavenue/ui/components/Divider"
import { IconArrowRightOutline } from "@kickavenue/ui/components/icons"
import Text from "@kickavenue/ui/components/Text"

import { formatPrice } from "@utils/misc"
import { TTransaction } from "types/transaction.type"
import useGetPaymentMethodById from "@hooks/useGetPaymentMethodById"
import { isQueryLoading } from "@utils/network"
import "react-loading-skeleton/dist/skeleton.css"
import SkeletonText from "@components/shared/Skeleton/SkeletonText"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"

const { PAYMENT_SUMMARY_PAYMENT } = ModalConstant.MODAL_IDS

const PaymentStatusSummary = ({
  transaction,
  isLoadingTxDetails,
}: {
  transaction?: TTransaction | null
  isLoadingTxDetails?: boolean
}) => {
  const { setOpen } = useModalStore()

  const qPaymentMethod = useGetPaymentMethodById({
    id: transaction?.paymentMethodId as number,
  })

  const { data: paymentMethod } = qPaymentMethod

  const isLoadingPaymentMethod = isQueryLoading(qPaymentMethod)

  return (
    <div className="w-full rounded-base border border-gray-w-80">
      <div className="flex flex-col gap-xs p-sm">
        <div className="flex justify-between">
          <Text size="sm" type="medium" state="primary">
            Total Payment
          </Text>
          <Text size="sm" type="medium" state="primary">
            {formatPrice(
              transaction?.paymentData?.amount as number,
              null,
              "IDR",
            )}
          </Text>
        </div>
        <div className="flex justify-between">
          <Text size="sm" type="medium" state="primary">
            Payment Method
          </Text>
          {isLoadingPaymentMethod && <SkeletonText className="w-1/5" />}
          {!isLoadingPaymentMethod && (
            <Text size="sm" type="medium" state="primary">
              {paymentMethod?.name}
            </Text>
          )}
        </div>
      </div>
      <Divider orientation="horizontal" state="default" />
      {!isLoadingTxDetails && (
        <Button
          variant="link"
          style={{ width: "100%", padding: "0" }}
          onClick={() => {
            setOpen(true, PAYMENT_SUMMARY_PAYMENT)
          }}
        >
          <div className="flex w-full cursor-pointer justify-between p-base">
            <Text size="sm" type="bold" state="primary">
              View Payment Summary
            </Text>
            <IconArrowRightOutline className="text-gray-w-40" />
          </div>
        </Button>
      )}
      {isLoadingTxDetails && (
        <div className="p-base">
          <SkeletonText className="w-full" />
        </div>
      )}
    </div>
  )
}

export default PaymentStatusSummary
