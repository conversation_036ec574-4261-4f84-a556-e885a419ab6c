import { useQuery } from "@tanstack/react-query"
import { useSession } from "next-auth/react"
import { useCallback, useMemo, useState } from "react"

import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { MemberApiRepository } from "@infrastructure/repositories/memberApiRepository"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useMemberStore } from "stores/memberStore"

export const usePersonalInformation = () => {
  const session = useSession()
  const memberRepository = useMemo(() => new MemberApiRepository(), [])
  const { setMember } = useMemberStore()

  const fetchMember = useCallback(async () => {
    const member = await memberRepository.getByMy()
    if (member) {
      setImagePreview(convertS3UrlToCloudFront(member.image))
      setMember(member)
    }
    return member
  }, [memberRepository, setMember])

  const { data, isLoading } = useQuery({
    queryKey: [QueryKeysConstant.GET_MY_MEMBER, session?.data?.user?.email],
    queryFn: () => fetchMember(),
    staleTime: 30000,
  })

  const [imagePreview, setImagePreview] = useState<string | null>(
    convertS3UrlToCloudFront(data?.image ?? "") ?? null,
  )

  return {
    member: data,
    isLoading,
    imagePreview,
    setImagePreview,
  }
}
