/* eslint-disable @typescript-eslint/naming-convention */

import { cx } from "class-variance-authority"
import { FC } from "react"

interface OrderStatus {
  id: number
  date: string
  status: string
  description: string
  isActive: boolean
}

const orderData: OrderStatus[] = [
  {
    id: 1,
    date: "2 Nov 2023, 23:13 WIB",
    status: "Out for Delivery",
    description: "Your package is on its way to your location",
    isActive: true,
  },
  {
    id: 2,
    date: "2 Nov 2023, 23:13 WIB",
    status: "In Transit",
    description: "Package has been picked up by courier",
    isActive: false,
  },
  {
    id: 3,
    date: "2 Nov 2023, 23:13 WIB",
    status: "Shipped",
    description: "Package has left our warehouse",
    isActive: false,
  },
  {
    id: 4,
    date: "2 Nov 2023, 23:13 WIB",
    status: "Processed",
    description: "Package is being prepared for shipping",
    isActive: false,
  },
  {
    id: 5,
    date: "2 Nov 2023, 23:13 WIB",
    status: "Order Placed",
    description: "Order has been confirmed",
    isActive: false,
  },
]

const OrderTrackingTimeline: FC = () => {
  return (
    <div className="mx-auto space-y-6">
      {orderData.map((item) => (
        <div key={item.id} className="flex gap-6">
          <div
            className={cx("w-44 text-base", {
              "text-gray": item.isActive,
              "text-gray-w-80": !item.isActive,
            })}
          >
            {item.date}
          </div>

          <div className="relative flex flex-col items-center">
            <div
              className={cx("h-2 w-2 rounded-full", {
                "bg-gray-b-65": item.isActive,
                "bg-gray-w-80": !item.isActive,
              })}
            />
            {item.id !== orderData.length - 1 && (
              <div
                className={cx("h-full w-px border-l-2 border-dashed", {
                  "border-gray-w-80": true,
                })}
              />
            )}
          </div>

          <div className="-mt-2 flex-1 text-base">
            <h3
              className={cx("font-bold", {
                "text-gray-b-65": item.isActive,
                "text-gray-w-80": !item.isActive,
              })}
            >
              {item.status}
            </h3>
            <p
              className={cx("text-sm", {
                "text-gray-600": item.isActive,
                "text-gray-w-80": !item.isActive,
              })}
            >
              {item.description}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

export default OrderTrackingTimeline
