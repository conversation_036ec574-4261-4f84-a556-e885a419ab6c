import {
  IconExpressBold,
  IconPreOrderBold,
  IconStandardBold,
} from "@kickavenue/ui/components"

import {
  getBadgeClassNameByListingItem,
  getBadgeTextByListingItem,
  getBadgeTypeByListingItem,
} from "@utils/listingItem"
import ListingBadge from "@shared/ListingBadge"
import { SellerListing } from "types/sellerListing"
import { TItemCondition } from "types/listingItem.type"

const RenderListingBadge = ({ listing }: { listing?: SellerListing }) => {
  const getBadgeIconLeft = () => {
    if (listing?.isPreOrder) {
      return IconPreOrderBold
    }
    if (listing?.isConsignment || listing?.isConsigment) {
      return IconExpressBold
    }
    return IconStandardBold
  }

  return (
    <ListingBadge
      isUsed={listing?.itemCondition === TItemCondition.Used}
      is99Percents={listing?.packagingCondition !== "PERFECT_BOX"}
      badgeType={getBadgeClassNameByListingItem(listing as SellerListing)}
      text={getBadgeTextByListingItem(listing as SellerListing)}
      type={getBadgeTypeByListingItem(listing as SellerListing)}
      iconLeft={getBadgeIconLeft()}
    />
  )
}

export default RenderListingBadge
