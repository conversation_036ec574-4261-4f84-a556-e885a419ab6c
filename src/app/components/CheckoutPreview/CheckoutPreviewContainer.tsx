"use client"

import { AuthRedirectWrapper } from "@components/AuthRedirectWrapper"
import { PageRouteConstant } from "@constants/pageRoute.constant"

import BuyNowOfferSegmented from "./BuyNowOfferSegmented"
import CheckoutPreviewDetail from "./CheckoutPreviewDetail"

const CheckoutPreviewContainer = () => {
  return (
    <AuthRedirectWrapper whenUnauthed={PageRouteConstant.LOGIN}>
      <BuyNowOfferSegmented />
      <CheckoutPreviewDetail />
    </AuthRedirectWrapper>
  )
}

export default CheckoutPreviewContainer
