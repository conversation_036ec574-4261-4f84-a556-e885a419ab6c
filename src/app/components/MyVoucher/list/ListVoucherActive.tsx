import React from "react"

import { useMyVoucherActive } from "../hooks/useMyVoucherActive"

import ListVoucher from "./ListVoucher"

export default function ListVoucherActive() {
  const {
    items,
    searchVoucher,
    isSuccess,
    isLoading,
    scrollContainerRef,
    searchQuery,
    isFetchingNextPage,
  } = useMyVoucherActive()

  return (
    <ListVoucher
      type="active"
      items={items}
      isLoading={isLoading}
      isSuccess={isSuccess}
      isFetchingNextPage={isFetchingNextPage}
      scrollContainerRef={scrollContainerRef}
      onModalOpen={() => {}}
      searchVoucher={searchVoucher}
      searchQuery={searchQuery}
    />
  )
}
