import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import { useConsignmentInProgressStore } from "stores/consignmentInProgressStore"
import { TTransactionDetailFilter } from "types/transactionDetail.type"

const { PAGE, PAGE_SIZE } = MiscConstant.FILTER_FIELDS

const useConsignmentInProgressPagination = () => {
  const { filter } = useConsignmentInProgressStore()
  const { handleChangeQuery } = useURLQuery()
  const { page, pageSize, totalPages } = (filter ||
    {}) as TTransactionDetailFilter

  const handleNext = () => {
    const nextPage = page + 1
    handleChangeQuery(PAGE, nextPage)
  }

  const handlePrev = () => {
    const prevPage = page - 1
    handleChangeQuery(PAGE, prevPage)
  }

  const handlePage = (page: number) => {
    handleChangeQuery(PAGE, page - 1)
  }

  const handlePerPage = (size: number) => {
    handleChangeQuery(PAGE_SIZE, size)
  }

  return {
    currentPage: page + 1,
    pageSize,
    totalPages: totalPages || 1,
    handleNext,
    handlePrev,
    handlePage,
    handlePerPage,
  }
}

export default useConsignmentInProgressPagination
