"use client"

import Spinner from "@components/shared/Spinner/Spinner"

import CardAddresses from "../CardAddresses"
import EmptyAddresses from "../EmptyAddresses"
import SearchAddresses from "../SearchAddresses"
import { useFetchAddress } from "../hooks/useFetchAddress"

export default function AddressList() {
  const { isLoading, addresses, searchQuery } = useFetchAddress()

  const renderAddresses = () => {
    switch (isLoading) {
      case true:
        return (
          <div className="flex size-full items-center justify-center">
            <Spinner />
          </div>
        )
      case false:
        if (addresses?.length === 0) {
          return <EmptyAddresses />
        }
        return <CardAddresses />
    }
  }

  return (
    <div className="flex h-full flex-col gap-y-base">
      {((addresses?.length ?? 0) > 0 || searchQuery) && <SearchAddresses />}
      {renderAddresses()}
    </div>
  )
}
