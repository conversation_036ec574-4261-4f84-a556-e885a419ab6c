import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { SellerConsignmentApiRepository } from "@infrastructure/repositories/SellerConsignmentApiRepository"
import useQuery from "@app/hooks/useQuery"
import { GetByIdSellerTransaction } from "@application/usecases/getByIdSellerTransaction"
import { TConsignmentTransactionResponse } from "types/sellerConsignment.type"

const useFetchSellerTransactionById = ({
  id,
  enabled = true,
  onSuccess,
}: {
  id?: number | null
  enabled?: boolean
  onSuccess?: (data: TConsignmentTransactionResponse) => void
}) => {
  const getSellerTransactionById = async () => {
    const r = new SellerConsignmentApiRepository()
    const u = new GetByIdSellerTransaction(r)
    const res = await u.execute(id!)
    onSuccess?.(res)
    return res
  }

  const query = useQuery({
    queryKey: [QueryKeysConstant.GET_SELLER_TRANSACTION_BY_ID, id],
    queryFn: getSellerTransactionById,
    enabled: enabled && Boolean(id),
  })

  return query
}

export default useFetchSellerTransactionById
