"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconAddOutline,
  IconArrowRightOutline,
  IconTrashOutline,
  Text,
} from "@kickavenue/ui/components"
import { useFormContext } from "react-hook-form"

import useGetAllMyBankAccount from "@app/hooks/useGetAllMyBankAccount"
import ClickableDiv from "@components/shared/ClickableDiv"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import useGetAllBankInfo from "@app/hooks/useGetAllBankInfo"
import { getBankIcon } from "@components/Profile/bank-account/BankAccountCard"

const { BANK_ACCOUNT_FORM, BANK_SELECTION } = ModalConstant.MODAL_IDS

const CashOutTo = () => {
  const { bankData } = useGetAllMyBankAccount()
  const { setOpen } = useModalStore()
  const { setValue, watch } = useFormContext()

  const selectedBank = watch("selectedBank")

  const handleOpenBankAccountFormModal = () => {
    setOpen(true, BANK_ACCOUNT_FORM)
  }

  const handleOpenBankSelection = () => {
    setOpen(true, BANK_SELECTION)
  }

  const setEmptyBank = () => {
    setValue("selectedBank", null)
  }

  const { data: allBankInfo } = useGetAllBankInfo({
    ids: bankData?.map((bank) => bank.bankId) || [],
    page: 0,
    pageSize: bankData.length,
  })

  const getBankInfo = (bankId: number) => {
    return allBankInfo?.content.find((bank) => bank.id === bankId)
  }

  return (
    <div className="flex flex-col gap-sm">
      <Text size="sm" type="bold" state="primary">
        Cash Out To
      </Text>

      {bankData?.length === 0 && (
        <>
          <Alert
            subTitle="Please add your bank account first to proceed"
            className="!w-full"
          />
          <Button
            variant="secondary"
            size="md"
            className="!w-full"
            onClick={handleOpenBankAccountFormModal}
            IconLeft={IconAddOutline}
          >
            Add New Bank Account
          </Button>
        </>
      )}

      {bankData?.length > 0 && (
        <div className="flex items-center justify-between gap-2 rounded-xl border border-gray-w-80 p-sm">
          {selectedBank && (
            <div className="flex w-full justify-between">
              <div className="flex w-full items-center gap-2">
                {getBankIcon(
                  getBankInfo(selectedBank.bankId)?.bankCode as string,
                )}
                <div className="flex flex-col">
                  <Text size="base" state="primary" type="bold">
                    {selectedBank.accountHolder}
                  </Text>
                  <Text size="sm" state="secondary" type="regular">
                    {getBankInfo(selectedBank.bankId)?.bankName}
                    <span className="mx-1 font-bold">·</span>{" "}
                    {selectedBank.accountNumber}
                  </Text>
                </div>
              </div>
              <Button
                variant="link"
                className="!p-0 !text-gray-w-40 hover:text-gray-w-60"
                onClick={() => setEmptyBank()}
              >
                <IconTrashOutline />
              </Button>
            </div>
          )}

          {!selectedBank && (
            <ClickableDiv
              className="flex w-full cursor-pointer items-center justify-between"
              onClick={handleOpenBankSelection}
              keyDownHandler={handleOpenBankSelection}
            >
              <Text state="primary" size="sm" type="bold">
                Select Cash Out to
              </Text>
              <IconArrowRightOutline className="size-5" />
            </ClickableDiv>
          )}
        </div>
      )}
    </div>
  )
}

export default CashOutTo
