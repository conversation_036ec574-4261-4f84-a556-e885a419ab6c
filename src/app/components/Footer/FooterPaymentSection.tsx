import { getDeliveryProviderList, getPaymentProviderList } from "./footer.utils"
import FooterAuthenticSection from "./FooterAuthenticSection"
import FooterMenuImageLink from "./FooterMenuImageLink"
import FooterTitle from "./FooterTitle"

const FooterPaymentSection = () => (
  <>
    <div className="col-span-4">
      <FooterTitle text="Authentic. Guaranteed." />
      <FooterAuthenticSection />
    </div>
    <div className="col-span-4">
      <FooterTitle text="Payment" />
      <div className="grid grid-cols-4 gap-x-lg gap-y-3">
        {getPaymentProviderList().map((item, idx) => (
          // eslint-disable-next-line react/no-array-index-key
          <FooterMenuImageLink key={idx} {...item} />
        ))}
      </div>
    </div>
    <div className="col-span-4">
      <FooterTitle text="Delivery" />
      <div className="grid grid-cols-4 gap-x-lg gap-y-3">
        {getDeliveryProviderList().map((item, idx) => (
          // eslint-disable-next-line react/no-array-index-key
          <FooterMenuImageLink key={idx} {...item} />
        ))}
      </div>
    </div>
  </>
)

export default FooterPaymentSection
