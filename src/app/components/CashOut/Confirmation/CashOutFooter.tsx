import { Button } from "@kickavenue/ui/components/Button"
import { useFormContext } from "react-hook-form"

import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"
import ByClickingProceedTermsCondition from "@components/ByClickingProceedTermsCondition/ByClickingProceedTermsCondition"

import { MIN_CASH_OUT_AMOUNT } from "./constants"

const CashOutFooter = ({ onProceed }: { onProceed: () => void }) => {
  const { currentBalanceNumber } = useGetCurrentBalanceAndFee()
  const { watch } = useFormContext()
  const amount = Number(watch("amount"))
  const bankId = watch("bankId")

  const isFormValid =
    Boolean(amount) &&
    Boolean(bankId) &&
    amount >= MIN_CASH_OUT_AMOUNT &&
    amount <= currentBalanceNumber

  return (
    <div className="flex flex-col gap-4 p-lg">
      <ByClickingProceedTermsCondition />
      <Button
        size="lg"
        variant="primary"
        className="!w-full"
        onClick={onProceed}
        disabled={!isFormValid}
      >
        Proceed
      </Button>
    </div>
  )
}

export default CashOutFooter
