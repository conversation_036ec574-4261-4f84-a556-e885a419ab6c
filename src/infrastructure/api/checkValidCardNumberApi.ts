import { httpClient } from "@infrastructure/providers/httpClient"
import { convertToCamelCase } from "@utils/misc"
import { TApiResponse } from "types/apiResponse.type"
import { BINData } from "types/bin.type"

export const checkValidCardNumberApi = {
  getByBinNumber: async (binNumber: string): Promise<TApiResponse<BINData>> => {
    const response = await httpClient.get(
      `/transaction/payment/bin/${binNumber}`,
    )
    const apiResponse = convertToCamelCase(
      response.data,
    ) as TApiResponse<BINData>
    return apiResponse
  },
}
