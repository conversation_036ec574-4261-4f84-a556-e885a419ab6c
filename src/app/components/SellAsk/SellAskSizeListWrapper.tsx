import { useEffect } from "react"

import CenterWrapper from "@app/components/shared/CenterWrapper"
import ProductImageSlider from "@components/ProductImageSlider"
import FixedBottomButton from "@components/shared/FixedBottomButton"
import { useMiscStore } from "stores/miscStore"
import { useProductStore } from "stores/productStore"
import { isAllSizeSelected } from "@utils/productDetail"
import useChangePage from "@app/hooks/useChangePage"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import SellerGuard from "@components/shared/SellerGuard"

import SellAskSizeList from "./SellAskSizeList"

const { SELLING } = PageRouteConstant

const SellAskSizeListWrapper = () => {
  const { selectedSize, detail: product } = useProductStore()
  const { setShowFooter } = useMiscStore()
  const { goToPage, isLoading } = useChangePage()
  const url = `${SELLING}/${product?.id}/${Object.keys(selectedSize || {})[0]}`
  useEffect(() => {
    setShowFooter(false)
    return () => setShowFooter(true)
  }, [setShowFooter])
  return (
    <SellerGuard>
      <CenterWrapper className="!pb-[50px] md:!pb-[200px]">
        <div className="col-span-4 md:col-span-6">
          <ProductImageSlider />
        </div>
        <div className="col-span-4 md:col-span-6">
          <SellAskSizeList />
        </div>
      </CenterWrapper>
      <FixedBottomButton
        disabled={
          !selectedSize || isAllSizeSelected(selectedSize) || isLoading[url]
        }
        onClick={() => goToPage(url)}
        text="Continue to Sell / Ask"
      />
    </SellerGuard>
  )
}

export default SellAskSizeListWrapper
