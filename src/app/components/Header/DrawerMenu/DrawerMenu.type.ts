import React, { HTMLProps } from "react"

export interface TLink {
  label: string
  id: string
  iconBold: React.ComponentType
  iconOutline: React.ComponentType
}

export default interface TDrawerMenuProps extends HTMLProps<HTMLDivElement> {
  visible?: boolean
  toggleDrawerMenu: () => void
  links: TLink[]
  linkActive: string
  isLogin: boolean
  name: string
  email: string
  urlProfile: string
  containerProps?: HTMLProps<HTMLDivElement>
}

export interface TDrawerMenuHeaderProps
  extends Omit<TDrawerMenuProps, "links" | "visible" | "containerProps"> {}
