import { Heading, Text } from "@kickavenue/ui/dist/src/components"

import { useProductStore } from "stores/productStore"

const SellAskTradeSummary = () => {
  const { detail: product } = useProductStore()
  return (
    <div className="col-span-4 flex items-center md:col-span-6">
      <div className="flex flex-col">
        <Heading heading="5" textStyle="bold">
          {product?.name || ""}
        </Heading>
        <Text size="base" state="secondary" type="regular">
          SKU: {product?.skuCode || ""}
        </Text>
      </div>
    </div>
  )
}

export default SellAskTradeSummary
