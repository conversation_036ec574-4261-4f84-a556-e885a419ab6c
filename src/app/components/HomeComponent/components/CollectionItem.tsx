/* eslint-disable @typescript-eslint/naming-convention */

import { useRouter } from "next/navigation"
import React from "react"

import ProductCard from "@components/shared/ProductCard"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { ISectionContentItem } from "types/menuWizard"

interface CollectionItemProps {
  item: ISectionContentItem
  productWishlist: any
  onWishlistClick: (item: any) => void
}

const CollectionItem: React.FC<CollectionItemProps> = ({
  item,
  productWishlist,
  onWishlistClick,
}) => {
  const router = useRouter()
  const validImageUrl = convertS3UrlToCloudFront(item.images[0] || "")
  const strikeThroughPrice =
    item?.underRetail && item.retailPrice
      ? `Rp ${parseFloat(item.retailPrice.amount).toLocaleString()}`
      : undefined
  const isWishlistActive = productWishlist
    ? Boolean(productWishlist[item.id])
    : false

  const handleGoDetail = () => {
    router.push(`/product/${item.id}`)
  }

  return (
    <div className="">
      <ProductCard
        cardContainer={{
          className: "w-full cursor-pointer",
          style: {
            width: "100%",
            "@media (min-width: 768px)": { width: "230.4px" },
          } as any,
        }}
        imageProps={{
          src: validImageUrl,
          width: 160,
          height: 230,
          alt: item.name,
          onClick: handleGoDetail,
          style: {
            width: "230px",
            height: "230px",
            "@media (min-width: 768px)": {
              width: "160px !important",
              height: "160px !important",
            },
          } as any,
          className: "w-full h-auto",
        }}
        brandName={item.brand.name}
        itemName={item.name}
        price={`Rp ${parseFloat(item.lowestPrice.amount).toLocaleString()}`}
        isWishlistActive={isWishlistActive}
        strikeThroughPrice={strikeThroughPrice}
        onWishlistClick={() => onWishlistClick(item)}
      />
    </div>
  )
}

export default CollectionItem
