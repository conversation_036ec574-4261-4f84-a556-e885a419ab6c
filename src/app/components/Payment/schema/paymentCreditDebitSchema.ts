import { z } from "zod"

import { FormFieldConstant } from "@constants/formField"

const {
  CARD_NUMBER,
  CARD_EXPIRY_MONTH,
  CARD_EXPIRY_YEAR,
  CARD_CVV,
  IS_AGREED,
} = FormFieldConstant.PAYMENT_CREDIT_DEBIT

export const paymentCreditDebitSchema = z.object({
  [CARD_NUMBER.KEY]: z
    .string()
    .min(1, "Card number is required")
    .refine(
      (value) => value.replaceAll(" ", "").replaceAll("_", "").length >= 16,
      "Please complete the card number",
    ),
  [CARD_EXPIRY_MONTH.KEY]: z
    .string()
    .min(1, "Expiry month is required")
    .refine((value) => {
      const month = parseInt(value, 10)
      return month >= 1 && month <= 12
    }, "Invalid month"),
  [CARD_EXPIRY_YEAR.KEY]: z
    .string()
    .min(1, "Expiry year is required")
    .refine((value) => {
      const year = parseInt(value, 10)
      const currentYear = new Date().getFullYear() % 100
      return year >= currentYear
    }, "Card expired"),
  [CARD_CVV.KEY]: z
    .string()
    .min(1, "CVV is required")
    .refine((value) => /^\d{3,4}$/.test(value), "Invalid CVV"),
  [IS_AGREED.KEY]: z
    .boolean()
    .refine((value) => value === true, "You must agree to the terms"),
})

export type PaymentCreditDebitFormValues = z.infer<
  typeof paymentCreditDebitSchema
>
