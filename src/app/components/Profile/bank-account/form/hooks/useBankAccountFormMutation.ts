import { useMutation } from "@tanstack/react-query"

import useToast from "@app/hooks/useToast"
import { QueryKeysConstant } from "@constants/queryKeys.constant"
import { bankApi } from "@infrastructure/api/bankApi"
import { queryClient } from "@lib/query-client"
import { useModalStore } from "stores/modalStore"

import { BankAccountFormData } from "../schema/bankAccountSchema"

interface BankAccountPayload {
  memberId: number
  accountHolder: string
  accountNumber: string
  bankId: number
}

export const useBankAccountFormMutation = ({
  bankAccountId,
  onSuccess,
}: {
  bankAccountId?: string | number
  onSuccess?: () => void
}) => {
  const { setOpen } = useModalStore()
  const { setShowToast } = useToast()

  const isEditMode = Boolean(bankAccountId)

  const preparePayload = (
    formData: BankAccountFormData,
  ): BankAccountPayload => {
    if (
      !formData.bankId ||
      !formData.accountNumber ||
      !formData.accountHolder
    ) {
      throw new Error("Missing required fields")
    }

    return {
      memberId: formData.memberId || 0,
      accountHolder: formData.accountHolder,
      accountNumber: formData.accountNumber,
      bankId: Number(formData.bankId.value),
    }
  }

  const handleSuccess = () => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_ALL_MY_BANK_ACCOUNT],
    })
    queryClient.invalidateQueries({
      queryKey: [QueryKeysConstant.GET_MY_BANK_ACCOUNT_BY_ID],
    })

    setShowToast(
      true,
      `Bank Account successfully ${isEditMode ? "updated" : "added"}`,
    )
    setOpen(false)

    onSuccess?.()
  }

  const handleError = (error: unknown) => {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to process bank account"
    setShowToast(true, errorMessage, "danger")
  }

  return useMutation({
    mutationFn: async (formData: BankAccountFormData) => {
      const payload = preparePayload(formData)

      try {
        if (isEditMode) {
          return bankApi.update(bankAccountId!, payload)
        }
        return bankApi.create(payload)
      } catch (error) {
        handleError(error)
        throw error
      }
    },
    onSuccess: handleSuccess,
    onError: handleError,
  })
}
