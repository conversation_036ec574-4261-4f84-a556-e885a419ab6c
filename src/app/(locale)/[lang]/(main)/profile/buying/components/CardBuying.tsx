import React, { ReactNode } from "react"
import { IconEyeSlashOutline } from "@kickavenue/ui/components/icons"
import { Heading } from "@kickavenue/ui/components"

export interface CreditProps {
  title: string
  icon: ReactNode
  credit: string
}

const CardBuying = (props: CreditProps) => {
  const { title, credit, icon } = props
  return (
    <div className="flex items-start rounded-xl border border-gray-w-80 p-3">
      <div className="mr-2 shrink-0">{icon}</div>
      <div className="flex grow flex-col">
        <span className="text-base font-normal text-gray">{title}</span>
        <Heading heading="5" textStyle="bold">
          {credit}
        </Heading>
      </div>
      <div className="ml-auto shrink-0 !text-green">
        <IconEyeSlashOutline className="text-gray-w-40" />
      </div>
    </div>
  )
}

export default CardBuying
