"use client"

import React from "react"
import { Tab, But<PERSON>, Text } from "@kickavenue/ui/components"
import { cx } from "class-variance-authority"

import styles from "./InviteAndEarn.module.scss"
import { useInviteAndEarn } from "./hooks/useInviteAndEarn"

export default function InviteAndEarn() {
  const { tabItems, tab, setTab, renderTabComponent, isTextActive } =
    useInviteAndEarn()

  return (
    <>
      <div
        className={cx(
          styles["invite-and-earn"],
          "overflow-y-auto p-sm lg:p-lg",
        )}
      >
        <h4 className="text-heading-4 font-bold text-gray-b-75">
          Invite and Earn
        </h4>
        <div className="my-lg overflow-hidden overflow-x-auto">
          <Tab className="!gap-x-xl lg:!w-full lg:!justify-start">
            {tabItems.map((item) => {
              return (
                <Button
                  data-active={item === tab}
                  onClick={() => setTab(item)}
                  variant="link"
                  key={item.name}
                  className={styles.button}
                >
                  <Text
                    size="base"
                    type={isTextActive(item)}
                    state="secondary"
                    className={styles.text}
                  >
                    {item.name}
                  </Text>
                </Button>
              )
            })}
          </Tab>
        </div>
        {renderTabComponent()}
      </div>
    </>
  )
}
