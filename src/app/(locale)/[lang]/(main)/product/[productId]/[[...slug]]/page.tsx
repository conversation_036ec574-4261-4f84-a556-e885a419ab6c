import React from "react"
import { Metadata } from "next"

import ProductDetailContainer from "@app/components/ProductDetailContainer"
import { prepareProductDetailData } from "@utils/productDetail"

export interface DetailProps {
  params: {
    productId: string
  }
}

async function getProductData(productId: number) {
  return prepareProductDetailData(productId, true)
}

export async function generateMetadata({
  params,
}: DetailProps): Promise<Metadata> {
  const productId = parseInt(params.productId, 10)
  const { product } = await getProductData(productId)

  return {
    title: product?.name || "product",
  }
}

export default async function Detail({ params }: DetailProps) {
  const productId = parseInt(params.productId, 10)
  const { product } = await getProductData(productId)

  return <ProductDetailContainer product={product} />
}
