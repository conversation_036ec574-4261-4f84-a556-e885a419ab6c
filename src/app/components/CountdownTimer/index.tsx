import React, { useState, useEffect } from "react"
import { IconTimeBulk } from "@kickavenue/ui/components"
import moment from "moment"

interface TimeState {
  hours: number
  minutes: number
  seconds: number
  milliseconds: number
}

interface CountdownTimeProps {
  endTime: string
}

const CountdownTime: React.FC<CountdownTimeProps> = ({ endTime }) => {
  const [time, setTime] = useState<TimeState>({
    hours: 0,
    minutes: 0,
    seconds: 0,
    milliseconds: 0,
  })

  useEffect(() => {
    const timer = setInterval(() => {
      const now = moment()
      const end = moment(endTime)
      const diff = end.diff(now)
      const duration = moment.duration(diff)

      setTime({
        hours: duration.hours(),
        minutes: duration.minutes(),
        seconds: duration.seconds(),
        milliseconds: duration.milliseconds(),
      })

      if (diff <= 0) {
        clearInterval(timer)
      }
    }, 100)

    return () => clearInterval(timer)
  }, [endTime])

  const formatTime = (value: number): string =>
    value.toString().padStart(2, "0")

  return (
    <div className="mt-4 w-[219px] rounded-sm border border-gray-w-80">
      <div className="flex items-center px-2 py-3">
        <div className="mr-2">
          <IconTimeBulk className="animate-pulse text-success" />
        </div>
        <div className="flex w-full justify-between text-base">
          {(Object.keys(time) as (keyof TimeState)[]).map((unit, index) => (
            <React.Fragment key={unit}>
              <div className="mr-2 transition-all duration-200 ease-in-out hover:scale-110">
                {formatTime(time[unit])}
              </div>
              {index < 3 && <div className="mr-2">:</div>}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CountdownTime
