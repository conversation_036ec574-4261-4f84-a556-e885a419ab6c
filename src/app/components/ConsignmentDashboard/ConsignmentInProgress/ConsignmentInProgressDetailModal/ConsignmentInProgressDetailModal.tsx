import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"
import Modal from "@components/shared/Modal"
import HeaderModal from "@components/shared/HeaderModal"

import ConsignmentInProgressDetailModalContent from "./ConsignmentInProgressDetailModalContent"

const { CONSIGNMENT_IN_PROGRESS_DETAIL } = ModalConstant.MODAL_IDS

const ConsignmentInProgressDetailModal = () => {
  const { setOpen, open, modalId } = useModalStore()

  if (!open || modalId !== CONSIGNMENT_IN_PROGRESS_DETAIL) return null

  return (
    <Modal modalId={CONSIGNMENT_IN_PROGRESS_DETAIL} className="!min-h-[612px]">
      <HeaderModal
        onClose={() => setOpen(false, CONSIGNMENT_IN_PROGRESS_DETAIL)}
        title="Sale Detail"
      />
      <ConsignmentInProgressDetailModalContent />
    </Modal>
  )
}

export default ConsignmentInProgressDetailModal
