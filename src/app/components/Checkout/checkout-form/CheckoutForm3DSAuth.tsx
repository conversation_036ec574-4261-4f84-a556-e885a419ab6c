import HeaderModal from "@components/shared/HeaderModal"
import Modal from "@components/shared/Modal"
import { ModalConstant } from "@constants/modal"
import { useModalStore } from "stores/modalStore"

interface CheckoutForm3DSAuthProps {
  redirectUrl?: string | null
  onModalClose?: () => void
}

const { CHECKOUT_3DS_AUTH } = ModalConstant.MODAL_IDS

const CheckoutForm3DSAuth = ({
  redirectUrl,
  onModalClose,
}: CheckoutForm3DSAuthProps) => {
  const { open, modalId } = useModalStore()

  const isOpen = open && modalId === CHECKOUT_3DS_AUTH
  if (!redirectUrl || !isOpen) return null

  return (
    <Modal modalId={CHECKOUT_3DS_AUTH} onClose={onModalClose}>
      <HeaderModal title="Card Authentication" />
      <iframe
        title="3ds-auth"
        style={{ height: "60vh", width: "100%" }}
        src={redirectUrl}
      />
    </Modal>
  )
}

export default CheckoutForm3DSAuth
