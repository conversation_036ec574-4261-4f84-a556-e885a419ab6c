import Image from "next/image"
import { Heading } from "@kickavenue/ui/components"

const bannerImageCategory = () => {
  return (
    <div className="relative size-full">
      <Image
        alt="banner"
        src="https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
        width={100}
        height={100}
        className="!h-[450px] !w-full object-cover"
      />
      <div className="absolute left-3 top-44 md:px-xxl">
        <div className="ml-16 max-w-lg text-white">
          <Heading heading="2" textStyle="bold">
            Kick Avenue Blog & News
          </Heading>
          <Heading heading="4" textStyle="regular">
            Subtext
          </Heading>
        </div>
      </div>
    </div>
  )
}

export default bannerImageCategory
