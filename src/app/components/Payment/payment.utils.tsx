import {
  IconAkulaku,
  IconAtome,
  IconBca,
  IconBni,
  IconBRI,
  IconGopay,
  IconKredivo,
  IconMandiri,
  IconPermataBank,
} from "@kickavenue/ui/components"
import TTextProps from "@kickavenue/ui/dist/src/components/Text/Text.type"

export const paymentInstallmentTypeItems = [
  {
    title: "Full Payment",
    titleProps: { type: "bold" } as TTextProps,
    withDivider: false,
    value: 0,
  },
  {
    title: "Installments 3x IDR 1,366,667",
    titleProps: { type: "bold" } as TTextProps,
    subtitle: "Including 0% interest",
    description: "Min. IDR 500,000 before transaction fees",
    withDivider: false,
    value: 3,
  },
  {
    title: "Installments 6x IDR 683,333",
    titleProps: { type: "bold" } as TTextProps,
    subtitle: "Including 0% interest",
    description: "Min. IDR 500,000 before transaction fees",
    value: 6,
  },
  {
    title: "Installments 12x IDR 341,667",
    titleProps: { type: "bold" } as TTextProps,
    subtitle: "Including 0% interest",
    description: "Min. IDR 500,000 before transaction fees",
    value: 12,
  },
]

export interface PaymentBankOption {
  id: number
  financeProvider: string
  financeProviderIcon: React.ReactNode
  financeProviderType?: "installment" | "virtual-account"
}

export const getIconFromFinanceProvider = (financeProvider: string) => {
  return (
    financeProviderOptions.find((option) =>
      financeProvider.toLowerCase().includes(option.financeProvider),
    )?.financeProviderIcon ?? <></>
  )
}

export const financeProviderOptions: PaymentBankOption[] = [
  {
    id: 1,
    financeProvider: "bca",
    financeProviderIcon: <IconBca />,
  },
  {
    id: 2,
    financeProvider: "mandiri",
    financeProviderIcon: <IconMandiri />,
  },
  {
    id: 3,
    financeProvider: "permata",
    financeProviderIcon: <IconPermataBank />,
  },
  {
    id: 4,
    financeProvider: "bni",
    financeProviderIcon: <IconBni />,
  },
  {
    id: 5,
    financeProvider: "bri",
    financeProviderIcon: <IconBRI />,
  },
  {
    id: 6,
    financeProvider: "gopay",
    financeProviderIcon: <IconGopay />,
  },
  {
    id: 7,
    financeProvider: "atome",
    financeProviderIcon: <IconAtome />,
  },
  {
    id: 8,
    financeProvider: "kredivo",
    financeProviderIcon: <IconKredivo />,
  },
  {
    id: 9,
    financeProvider: "akulaku",
    financeProviderIcon: <IconAkulaku />,
  },
]
