import React from "react"
import {
  IconArrowDownOutline,
  IconArrowUpOutline,
} from "@kickavenue/ui/components"

interface FilterHeaderProps {
  title: string
  isOpen: boolean
  toggleOpen: () => void
}

export const FilterHeader: React.FC<FilterHeaderProps> = ({
  title,
  isOpen,
  toggleOpen,
}) => {
  const iconArrow = isOpen ? <IconArrowUpOutline /> : <IconArrowDownOutline />

  return (
    <div
      className="mb-3 flex cursor-pointer items-center justify-between"
      onClick={toggleOpen}
      role="button"
      tabIndex={0}
      onKeyPress={toggleOpen}
    >
      <div className="text-base font-bold">{title}</div>
      <div className="cursor-pointer">{iconArrow}</div>
    </div>
  )
}
