import Link from "next/link"
import React from "react"

import { TLink } from "./DrawerMenu.type"
import styles from "./DrawerMenu.module.css"

interface TDrawerMenuLinkProps {
  links: TLink[]
  linkActive: string
}

const DrawerMenuLink: React.FC<TDrawerMenuLinkProps> = ({
  links,
  linkActive,
}) => {
  const isLinkActive = (linkId: string) =>
    linkActive === linkId ? "true" : "false"

  const renderLinkActiveIcon = (link: TLink) =>
    linkActive === link.id ? <link.iconBold /> : <link.iconOutline />

  if (!links || links.length === 0) {
    return <div className={styles.link}>No links available</div>
  }

  return (
    <ul className={styles.link}>
      {links.map((link) => (
        <Link key={link.id} href={link.id}>
          <li data-is-active={isLinkActive(link.id)}>
            {renderLinkActiveIcon(link)}
            {link.label}
          </li>
        </Link>
      ))}
    </ul>
  )
}

export default DrawerMenuLink
