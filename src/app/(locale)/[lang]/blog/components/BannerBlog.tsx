"use client"

import { Carousel } from "@kickavenue/ui/components/Carousel/Carousel"
import { Button, Heading } from "@kickavenue/ui/dist/src/components"
import Image from "next/image"

const BannerBlog = () => {
  const data = [
    {
      id: 1,
      name: "",
      link: "https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      id: 2,
      name: "",
      link: "https://images.unsplash.com/photo-1717137389747-d1d4ced6abc8?q=80&w=2500&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
  ]

  return (
    <div className="h-[450px]">
      <Carousel
        size="lg"
        variant="line"
        className="h-full"
        withBackground
        isLoop
        isInfinite
        loopIntervalMs={5000}
      >
        {data?.map((item) => (
          <div key={item.id} className="relative size-full">
            <Image
              alt={item.name || `Banner ${item.id}`}
              src={item.link}
              width="100"
              height="100"
              className="size-full object-cover"
            />
            <div className="absolute top-44">
              <div className="ml-16 max-w-lg text-white">
                <Heading heading="2" textStyle="bold">
                  Kick Avenue Blog & News
                </Heading>
                <Heading heading="4" textStyle="regular">
                  Subtext
                </Heading>
                <Button size="md" variant="secondary" className="mt-4">
                  Read Now
                </Button>
              </div>
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  )
}

export default BannerBlog
