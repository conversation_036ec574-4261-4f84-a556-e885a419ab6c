import { useEffect, useState } from "react"
import { Badge, Heading, Text } from "@kickavenue/ui/components/index"

import { formatDate } from "@utils/misc"
import { TTransaction } from "types/transaction.type"
import IconTimeBold from "@components/shared/Icons/IconTimeBold"

import {
  getAwaitingPaymentDeadline,
  getAwaitingPaymentDeadlineTimer,
} from "./paymentStatus.utils"

const PaymentStatusAwaitingHeading = ({
  transaction,
}: {
  transaction?: TTransaction
}) => {
  const [timeDisplay, setTimeDisplay] = useState("")
  const deadline = getAwaitingPaymentDeadline(transaction)

  useEffect(() => {
    const updateTimer = () => {
      const { time } = getAwaitingPaymentDeadlineTimer(transaction)
      setTimeDisplay(time || "-")
    }

    updateTimer()
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [transaction])

  const getFormattedDeadline = () => {
    if (!deadline) return "-"
    return `${formatDate(deadline, "DD MMMM YYYY, HH:mm")} WIB`
  }

  return (
    <div className="flex flex-col items-center">
      <Heading heading="5" textStyle="bold" className="mb-base">
        Awaiting Payment
      </Heading>
      <Badge
        type="negative"
        text={timeDisplay}
        size="md"
        iconLeft={IconTimeBold}
        className="!mb-base [&>svg]:!text-[#FF2323]"
      />
      <Text
        size="base"
        type="regular"
        state="secondary"
        className="mb-xs text-center md:w-[316px]"
      >
        Please complete your payment before
      </Text>
      <Text size="base" type="bold" state="primary">
        {getFormattedDeadline()}
      </Text>
    </div>
  )
}

export default PaymentStatusAwaitingHeading
