const path = require("path")

const kickUi = path.join(__dirname, "./src/**/*.{js,jsx,ts,tsx}")
const commonLib = path.join(
  __dirname,
  "./node_modules/@kickavenue/ui/dist/**/*.{js,jsx,ts,tsx,css}",
)
const plugin = require("tailwindcss/plugin")
const typography = require("@tailwindcss/typography")

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [kickUi, commonLib],
  theme: {
    fontSize: {
      base: [
        "14px",
        {
          lineHeight: "24px",
        },
      ],
      sm: [
        "12px",
        {
          lineHeight: "17px",
        },
      ],
      xs: [
        "10px",
        {
          lineHeight: "14px",
        },
      ],
      "heading-1": [
        "35px",
        {
          lineHeight: "50px",
        },
      ],
      "heading-2": [
        "29px",
        {
          lineHeight: "41px",
        },
      ],
      "heading-3": [
        "24px",
        {
          lineHeight: "34px",
        },
      ],
      "heading-4": [
        "20px",
        {
          lineHeight: "28px",
        },
      ],
      "heading-5": [
        "17px",
        {
          lineHeight: "24px",
        },
      ],
    },
    colors: {
      transparent: "transparent",
      black: {
        DEFAULT: "#000000",
        "dim-20": "rgba(0, 0, 0, 0.2)",
        "dim-40": "rgba(0, 0, 0, 0.4)",
        "dim-60": "rgba(0, 0, 0, 0.6)",
      },
      white: "#FFFFFF",
      gray: {
        "b-75": "#191919",
        "b-65": "#242424",
        "b-50": "#333333",
        "b-40": "#3D3D3D",
        DEFAULT: "#666666",
        "w-40": "#A3A3A3",
        "w-60": "#C2C2C2",
        "w-80": "#E0E0E0",
        "w-90": "#F0F0F0",
        "w-95": "#F7F7F7",
      },
      green: {
        "b-70": "#03251F",
        "b-50": "#063D34",
        "b-30": "#085549",
        "b-10": "#0A6E5E",
        DEFAULT: "#0B7A68",
        "w-30": "#54A295",
        "w-50": "#85BDB4",
        "w-70": "#B6D7D2",
        "w-90": "#E7F2F0",
      },
      red: {
        "b-70": "#2D0007",
        "b-50": "#4B000C",
        "b-30": "#690011",
        "b-10": "#870016",
        DEFAULT: "#960018",
        "w-30": "#B64D5D",
        "w-50": "#CA808B",
        "w-70": "#DFB3BA",
        "w-90": "#F5E5E8",
      },
      success: {
        DEFAULT: "#4CAF50",
        "w-70": "#C9E7CB",
        "w-90": "#EDF7ED",
      },
      warning: {
        DEFAULT: "#FA8C15",
        "w-70": "#FEDDB9",
        "w-90": "#FFF4E8",
      },
      danger: {
        "b-30": "#B31818",
        DEFAULT: "#FF2323",
        "w-30": "#FF6565",
        "w-70": "#FFBDBD",
        "w-90": "#FFE9E9",
      },
      accent: {
        DEFAULT: "#2E90FA",
        "w-70": "#C0DEFE",
        "w-90": "#EAF4FF",
      },
      text: {
        DEFAULT: "#242424",
        secondary: "#666666",
      },
      icon: {
        DEFAULT: "#242424",
        secondary: "#A3A3A3",
      },
      surface: {
        primary: "#FFFFFF",
        secondary: "#F7F7F7",
      },
      border: "#E0E0E0",
      divider: "#E0E0E0",
      link: {
        DEFAULT: "#0B7A68",
        pressed: "#0A6E5E",
        hover: "#54A295",
        disabled: "#B6D7D2",
      },
    },
    boxShadow: {
      none: "0 0 #0000",
      base: "0px 0px 8px 0px rgba(36, 36, 36, 0.08)",
      secondary: "0px 0px 12px 0px rgba(36, 36, 36, 0.12)",
      tertiary: "0px 4px 24px 0px rgba(36, 36, 36, 0.12)",
    },
    extend: {
      spacing: {
        xxxs: "2px",
        xxs: "4px",
        xs: "8px",
        sm: "12px",
        base: "16px",
        md: "20px",
        lg: "24px",
        xl: "32px",
        xxl: "40px",
      },
      borderRadius: {
        xxs: "4px",
        xs: "6px",
        sm: "8px",
        base: "12px",
        lg: "16px",
        full: "9999px",
      },
      gap: {
        xxl: "2rem",
      },
      fontFamily: {
        "plus-jakarta": ["var(--font-plus-jakarta)", "sans-serif"],
      },
      lineHeight: {
        xxxs: "2px",
        xxs: "4px",
        xs: "8px",
        sm: "12px",
        base: "16px",
        md: "20px",
        lg: "24px",
        xl: "32px",
        xxl: "40px",
      },
      screens: {
        xs: "320px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        "3xl": "1920px",
        "4xl": "2560px",
        "5xl": "3200px",
      },
    },
  },
  plugins: [
    plugin(({ addComponents }) => {
      addComponents({})
      addComponents({})
    }),
    typography,
  ],
}
