"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { Divider, Heading, IconCloseOutline } from "@kickavenue/ui/components"
import { useSearchParams } from "next/navigation"
import { FormProvider, useForm } from "react-hook-form"

import Modal from "@app/components/shared/Modal"
import useGetBankInfoById from "@app/hooks/useGetBankInfoById"
import useGetMyBankById from "@app/hooks/useGetMyBankById"
import { useReplaceQueryParams } from "@app/hooks/useReplaceQueryParams"
import ButtonSave from "@components/shared/Form/ButtonSave"
import { ModalConstant } from "@constants/modal"
import { useMemberStore } from "stores/memberStore"
import { useModalStore } from "stores/modalStore"

import BankAccountFormFields from "./FormFields"
import { useBankAccountFormMutation } from "./hooks/useBankAccountFormMutation"
import {
  BankAccountFormData,
  bankAccountSchema,
} from "./schema/bankAccountSchema"

const BankAccountFormModal = () => {
  const replaceQueryParams = useReplaceQueryParams()
  const { setOpen } = useModalStore()
  const { member } = useMemberStore()
  const searchParams = useSearchParams()
  const isEditMode = searchParams.get("mode") === "edit"
  const titleModal = isEditMode ? "Edit Bank Account" : "New Bank Account"
  const bankAccountId = searchParams.get("bankAccountId") || 0
  const { myBankData } = useGetMyBankById(bankAccountId)
  const { data: bankInfo } = useGetBankInfoById({
    id: myBankData?.bankId,
  })

  const onCloseFormModal = () => {
    setOpen(false, ModalConstant.MODAL_IDS.BANK_ACCOUNT_FORM)

    // remove query params after successfully adding or editing bank account
    replaceQueryParams({}, ["mode", "bankAccountId"])
  }

  const mutation = useBankAccountFormMutation({
    bankAccountId,
    onSuccess: () => {
      onCloseFormModal()
    },
  })

  const methods = useForm<BankAccountFormData>({
    resolver: zodResolver(bankAccountSchema),
    values: {
      bankId: bankInfo
        ? {
            value: bankInfo.id.toString() ?? "",
            label: bankInfo.bankName ?? "",
          }
        : null,
      accountNumber: myBankData?.accountNumber ?? "",
      accountHolder: myBankData?.accountHolder ?? "",
      memberId: member?.id ?? 0,
    },
  })

  const {
    formState: { isSubmitting, isValid: isFormValid },
    handleSubmit,
  } = methods

  const onSubmit = async (data: BankAccountFormData) => {
    mutation.mutate(data)
  }

  return (
    <Modal
      modalId={ModalConstant.MODAL_IDS.BANK_ACCOUNT_FORM}
      onClose={onCloseFormModal}
    >
      <FormProvider {...methods}>
        <div className="flex items-center justify-between p-md">
          <Heading heading="5" textStyle="bold" className="mx-auto">
            {titleModal}
          </Heading>
          <IconCloseOutline
            onClick={onCloseFormModal}
            className="!h-6 !w-6 shrink-0 cursor-pointer"
          />
        </div>

        <Divider />

        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
          <BankAccountFormFields />

          <div className="mt-auto flex flex-col p-md">
            <ButtonSave loading={isSubmitting} disabled={!isFormValid} />
          </div>
        </form>
      </FormProvider>
    </Modal>
  )
}

export default BankAccountFormModal
