"use client"

import {
  IconEyeOutline,
  IconEyeSlashOutline,
  IconKickPointsBulk,
  Text,
  Alert,
} from "@kickavenue/ui/components"
import { useState } from "react"

import { formatPrice } from "@utils/misc"
import { EBalanceType } from "types/balance.type"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"
import { MiscConstant } from "@constants/misc"

const KickPointBalance = () => {
  const { currentBalanceNumber } = useGetCurrentBalanceAndFee(
    EBalanceType.KickPoint,
  )
  const [showBalance, setShowBalance] = useState(true)

  const toggleBalance = () => {
    setShowBalance(!showBalance)
  }

  const displayBalanceValue = showBalance
    ? formatPrice(currentBalanceNumber, null, "KP", "KP 0")
    : MiscConstant.BULLET_MASK

  const eyeIcon = showBalance ? (
    <IconEyeSlashOutline className="size-5 text-gray-w-40" />
  ) : (
    <IconEyeOutline className="size-5 text-gray-w-40" />
  )

  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between rounded-xl border border-gray-w-80 p-sm">
        <div className="flex items-center gap-2">
          <IconKickPointsBulk color="#0B7A68" className="size-6" />
          <Text size="base" state="secondary" type="regular">
            Balance
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Text size="base" type="bold" state="primary">
            {displayBalanceValue}
          </Text>
          <button
            type="button"
            onClick={toggleBalance}
            className="flex items-center justify-center"
          >
            {eyeIcon}
          </button>
        </div>
      </div>
      <div className="mt-6 w-full">
        <Alert
          className="!w-full shadow-base"
          subTitle="Please note that Kick Points are not electronic money, so they cannot be cashed out or transferred."
          variant="information"
        />
      </div>
    </div>
  )
}

export default KickPointBalance
