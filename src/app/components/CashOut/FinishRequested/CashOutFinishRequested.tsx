"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text } from "@kickavenue/ui/components"
import {
  IconArrowRightOutline,
  IconKickCreditBulkColor,
  IconQuestionmarkCircleBold,
  IconSellerCreditBulkColor,
  IconSuccessPaymentBulk,
} from "@kickavenue/ui/components/icons"
import Link from "next/link"

import { PageRouteConstant } from "@constants/pageRoute.constant"
import useBalanceType from "@app/hooks/useBalanceType"
import { useModalStore } from "stores/modalStore"
import { ModalConstant } from "@constants/modal"
import useGetCurrentBalanceAndFee from "@app/hooks/useGetCurrentBalanceAndFee"

const CashOutFinishRequested = () => {
  const { isKickCredit } = useBalanceType()
  const { currentBalance } = useGetCurrentBalanceAndFee()

  const { setOpen } = useModalStore()
  const { KICK_CREDIT_INFO, SELLER_CREDIT_INFO } = ModalConstant.MODAL_IDS

  const handleOpenCreditInfo = () => {
    if (isKickCredit) {
      setOpen(true, KICK_CREDIT_INFO)
    } else {
      setOpen(true, SELLER_CREDIT_INFO)
    }
  }

  const textContents = {
    title: isKickCredit ? "Kick Credit" : "Seller Credit",
    creditBalance: isKickCredit
      ? "Kick Credit Balance"
      : "Seller Credit Balance",
    icon: isKickCredit ? (
      <IconKickCreditBulkColor className="size-6" />
    ) : (
      <IconSellerCreditBulkColor className="size-6" />
    ),
    linkToCredit: isKickCredit
      ? PageRouteConstant.PROFILE_KICK_CREDIT
      : PageRouteConstant.PROFILE_SELLER_CREDIT,
  }

  return (
    <div className="flex w-full items-center justify-center bg-gray-w-95 py-lg">
      <div className="flex w-full max-w-[612px] flex-col items-center rounded-xl bg-white">
        <div className="flex flex-col items-center justify-center gap-lg p-lg">
          <div className="flex justify-center">
            <IconSuccessPaymentBulk className="size-16 text-success" />
          </div>

          <h1 className="text-center text-heading-4 font-bold">
            Cash Out Requested
          </h1>

          <div className="flex w-full flex-col gap-2 rounded-sm border border-solid border-gray-w-80 p-sm">
            <div className="flex items-center gap-3">
              <div className="text-[#0B7A68]">{textContents.icon}</div>
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-1">
                  <Text size="sm" state="primary" type="bold">
                    {textContents.creditBalance}
                  </Text>
                  <IconQuestionmarkCircleBold
                    className="size-4 cursor-pointer"
                    onClick={handleOpenCreditInfo}
                  />
                </div>
                <Text size="sm" state="secondary" type="regular">
                  {currentBalance}
                </Text>
              </div>
            </div>
          </div>

          <Alert
            style={{ width: "100%" }}
            subTitle="If you have any question about your cash out, please contact our team by email to
              <EMAIL>. Please put your invoice number into your subject email."
            isIcon
          />
        </div>

        <div className="w-full">
          <Divider orientation="horizontal" />
        </div>

        <div className="grid w-full grid-cols-2 gap-4 p-lg">
          <Link href={PageRouteConstant.HOME} className="col-span-1">
            <Button variant="secondary" size="lg" className="!w-full">
              Back to Home Page
            </Button>
          </Link>

          <Link href={textContents.linkToCredit} className="col-span-1">
            <Button
              variant="primary"
              size="lg"
              className="!w-full"
              IconRight={IconArrowRightOutline}
            >
              Go to Pending Cash Out
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default CashOutFinishRequested
