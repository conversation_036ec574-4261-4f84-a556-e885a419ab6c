"use client"

import { useEffect } from "react"
import { useParams, useSearchParams } from "next/navigation"

import { useBuyOrOfferStore } from "stores/buyOrOfferStore.ts"
import useFetchListingItemById from "@app/hooks/useFetchListingItemById"
import { getBooleanValue } from "@utils/query.utils"
import Spinner from "@components/shared/Spinner"
import { ECheckoutPreviewSearchKey } from "types/misc.type"
import { useListingItemStore } from "stores/listingItemStore"
import { TBuyingOfferText } from "types/buyingOffer.type"
import { generateListingItem } from "@utils/listingItem"
import { useCheckoutStore } from "stores/checkoutStore"
import { usePaymentStore } from "stores/paymentStore"

import CheckoutPreviewDetailBuyNow from "./CheckoutPreviewDetailBuyNow"
import useLoadCheckoutData from "./hooks/useLoadCheckoutData"
import CheckoutPreviewMakeOffer from "./CheckoutPreviewMakeOffer"

const { Key } = ECheckoutPreviewSearchKey
const { BUY_NOW, MAKE_OFFER } = TBuyingOfferText

const CheckoutPreviewDetail = () => {
  const { option, setOption } = useBuyOrOfferStore()
  const { listingid } = useParams<{ listingid: string }>()
  const { setListingItemDetail } = useListingItemStore()
  const {
    setAddon,
    setVoucher,
    setShippingFee,
    setProcessingFee,
    setItemPrices,
  } = useCheckoutStore()
  const { resetPaymentTypeAndToken } = usePaymentStore()

  const searchParams = useSearchParams()
  const key = getBooleanValue(searchParams.get(Key))

  useLoadCheckoutData()

  useEffect(() => {
    return () => {
      setListingItemDetail(generateListingItem())
    }
  }, [setListingItemDetail])

  useEffect(() => {
    if (key) {
      setOption(BUY_NOW)
      return
    }
    setOption(MAKE_OFFER)
  }, [key, setOption])

  useEffect(() => {
    if (!key) return
    setAddon([])
    setVoucher(undefined)
    setShippingFee(null)
    setProcessingFee(null)
    setItemPrices([])
    resetPaymentTypeAndToken()
  }, [
    key,
    setOption,
    setAddon,
    setVoucher,
    setShippingFee,
    setProcessingFee,
    setItemPrices,
    resetPaymentTypeAndToken,
  ])

  const { isLoading } = useFetchListingItemById(
    parseInt(listingid, 10),
    key,
    (listingItem) => {
      setListingItemDetail(listingItem)
      setOption(listingItem?.id ? BUY_NOW : MAKE_OFFER)
    },
  )

  if (isLoading && key) {
    return (
      <div className="flex min-h-[300px] items-center justify-center md:min-h-[500px]">
        <Spinner />
      </div>
    )
  }

  if (option === MAKE_OFFER) {
    return <CheckoutPreviewMakeOffer />
  }

  return <CheckoutPreviewDetailBuyNow />
}

export default CheckoutPreviewDetail
