import { z } from "zod"

const bankOptionSchema = z
  .object({
    label: z.string(),
    value: z.string(),
  })
  .nullable()

export const bankAccountSchema = z.object({
  bankId: bankOptionSchema,
  accountNumber: z
    .string()
    .min(1, "Account number is required")
    .regex(/^\d+$/, "Account number must contain only numbers"),
  accountHolder: z.string().min(1, "Account holder name is required"),
  memberId: z.number().optional(),
})

export type BankAccountFormData = z.infer<typeof bankAccountSchema>
