"use client"

import { usePathname } from "next/navigation"
import { useEffect } from "react"

import useHeaderMenuDrawer from "@app/hooks/useHeaderMenuDrawer"

import DrawerMenu from "./DrawerMenu"

export interface HeaderMenuDrawerProps {
  showDrawer: boolean
  setShowDrawer: (showDrawer: boolean) => void
}

const HeaderMenuDrawer = ({
  showDrawer,
  setShowDrawer,
}: HeaderMenuDrawerProps) => {
  const pathname = usePathname()
  const { defaultProps } = useHeaderMenuDrawer()

  const drawerMenuProps = defaultProps

  useEffect(() => {
    setShowDrawer(false)
  }, [pathname, setShowDrawer])

  return (
    <div className="md:hidden">
      <DrawerMenu
        {...drawerMenuProps}
        toggleDrawerMenu={() => setShowDrawer(!showDrawer)}
        visible={showDrawer}
        linkActive={pathname}
      />
    </div>
  )
}

export default HeaderMenuDrawer
