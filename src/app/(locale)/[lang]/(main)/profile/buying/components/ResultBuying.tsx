"use client"

import {
  IconTotalPurchaseBulk,
  IconSuccessTransactionBulk,
  IconAverageOrderValueBulk,
} from "@kickavenue/ui/components"

import SummaryCard from "@shared/SummaryCard"
import { formatPrice } from "@utils/misc"
import useFetchBuyingSummary from "@app/hooks/useFetchBuyingSummary"
import { formatNumberWithSeparator } from "@utils/separator"

export default function ResultBuying() {
  const { data, isLoading } = useFetchBuyingSummary()

  return (
    <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-3">
      <SummaryCard
        value={formatPrice(data?.totalPurchase ?? 0, null, "IDR")}
        isLoading={isLoading}
        Icon={<IconTotalPurchaseBulk width={24} height={24} color="#0b7a68" />}
        title="Total Purchase"
      />
      <SummaryCard
        value={formatNumberWithSeparator(data?.completedPurchase ?? 0, ",")}
        isLoading={isLoading}
        Icon={
          <IconSuccessTransactionBulk width={24} height={24} color="#0b7a68" />
        }
        title="Completed Purchases"
      />
      <SummaryCard
        value={formatPrice(data?.averagePurchaseValue ?? 0, null, "IDR")}
        isLoading={isLoading}
        Icon={
          <IconAverageOrderValueBulk width={24} height={24} color="#0b7a68" />
        }
        title="Average Purchase Value"
      />
    </div>
  )
}
