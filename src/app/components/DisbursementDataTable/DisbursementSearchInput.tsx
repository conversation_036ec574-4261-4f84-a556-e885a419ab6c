import { Input, IconSearchOutline } from "@kickavenue/ui/components"
import React from "react"

interface DisbursementSearchInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

const DisbursementSearchInput = ({
  value,
  onChange,
  placeholder = "Search by disbursement number",
}: DisbursementSearchInputProps) => {
  return (
    <Input
      value={value}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
        onChange(e.target.value)
      }
      placeholder={placeholder}
      leftIcon={<IconSearchOutline />}
      className="!w-full !border-gray-w-95 bg-gray-w-95 md:!w-1/3"
    />
  )
}

export default DisbursementSearchInput
