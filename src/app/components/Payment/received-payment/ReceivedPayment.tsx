"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  IconArrowRightOutline,
  IconSuccessPaymentBulk,
  Space,
  Text,
} from "@kickavenue/ui/components"
import { useRouter } from "next/navigation"

import PaymentSummaryModal from "@components/Payment/payment-summary/PaymentSummaryModal"
import CodeWithCopyButton from "@components/shared/CodeWithCopyButton"
import { ModalConstant } from "@constants/modal"
import { formatCurrencyStripe } from "@utils/misc"
import { useModalStore } from "stores/modalStore"
import { TPaymentMethodData } from "types/paymentMethod.type"
import { TTransactionResponse } from "types/transaction.type"

const OrderIdSection = ({
  transactionData,
}: {
  transactionData?: TTransactionResponse
}) => (
  <div className="items w-full rounded-base border border-gray-w-80">
    <div className="flex items-center justify-between gap-xs p-base">
      <Text size="sm" type="medium" state="primary">
        Order ID
      </Text>
      <CodeWithCopyButton code={transactionData?.orderId || ""} />
    </div>
  </div>
)

const PaymentSummarySection = ({
  transactionData,
  paymentMethods,
}: {
  transactionData?: TTransactionResponse
  paymentMethods?: TPaymentMethodData
}) => {
  const { setOpen } = useModalStore()
  const { PAYMENT_SUMMARY_PAYMENT } = ModalConstant.MODAL_IDS

  return (
    <div className="w-full rounded-base border border-gray-w-80">
      <div className="flex flex-col gap-xs p-base">
        <div className="flex justify-between">
          <Text size="sm" type="medium" state="primary">
            Total Payment
          </Text>
          <Text size="sm" type="medium" state="primary">
            {formatCurrencyStripe({ price: transactionData?.totalAmount })}
          </Text>
        </div>
        <div className="flex justify-between">
          <Text size="sm" type="medium" state="primary">
            Payment Method
          </Text>
          <Text size="sm" type="medium" state="primary">
            {paymentMethods?.name}
          </Text>
        </div>
      </div>
      <Divider orientation="horizontal" state="default" />
      <Button
        variant="link"
        style={{ width: "100%", padding: "0" }}
        onClick={() => setOpen(true, PAYMENT_SUMMARY_PAYMENT)}
      >
        <div className="flex w-full cursor-pointer justify-between p-base">
          <Text size="sm" type="bold" state="primary">
            View Payment Summary
          </Text>
          <IconArrowRightOutline className="text-gray-w-40" />
        </div>
      </Button>
    </div>
  )
}

const ActionButtons = () => {
  const router = useRouter()

  return (
    <div className="flex w-full justify-between gap-base">
      <Button
        size="lg"
        variant="secondary"
        style={{ width: "100%" }}
        onClick={() => router.push("/search")}
      >
        Back to Market
      </Button>
      <Button
        size="lg"
        variant="primary"
        style={{ width: "100%" }}
        IconRight={() => <IconArrowRightOutline className="text-gray-w-40" />}
        onClick={() => router.push("/buying")}
      >
        Buying Dashboard
      </Button>
    </div>
  )
}

export interface ReceivedPaymentProps {
  transactionData?: TTransactionResponse
  paymentMethods?: TPaymentMethodData
}

export default function ReceivedPayment({
  transactionData,
  paymentMethods,
}: ReceivedPaymentProps) {
  const headerText =
    "We're getting your order ready. Track your order status in the Buying Dashboard."

  return (
    <>
      <PaymentSummaryModal />
      <div className="bg-gray-w-95 p-xxl md:flex md:justify-center">
        <div className="rounded-base bg-white md:w-[564px] md:p-lg">
          <div className="flex flex-col items-center gap-base">
            <IconSuccessPaymentBulk className="size-[80px] text-success" />
            <Heading heading="5" textStyle="bold">
              Payment Received
            </Heading>
            <Text
              size="base"
              type="regular"
              state="secondary"
              className="text-center md:w-[316px]"
            >
              {headerText}
            </Text>
            <Text size="base" type="bold" state="primary">
              {formatCurrencyStripe({ price: transactionData?.totalAmount })}
            </Text>
            <Space direction="y" size="xxs" type="margin" />

            <OrderIdSection transactionData={transactionData} />
            <PaymentSummarySection
              transactionData={transactionData}
              paymentMethods={paymentMethods}
            />

            <Alert
              style={{ width: "100%" }}
              subTitle="If you have any question about your order, please contact our team by <NAME_EMAIL>. Please put your invoice number into your subject email."
              isIcon
            />
            <Divider orientation="horizontal" state="default" />

            <ActionButtons />
          </div>
        </div>
      </div>
    </>
  )
}
