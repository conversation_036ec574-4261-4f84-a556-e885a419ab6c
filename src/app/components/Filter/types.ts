export interface FilterItem {
  id: number | string
  name: string
  color?: string
}

export interface SizeOption {
  value: string
  label: string
}

export interface BaseFilterGroupProps {
  title: string
  showSearch?: boolean
  maxItems?: number
  isParentSearch?: boolean
  showMoreButton?: boolean
  onFilterChange?: (selectedItems: string[]) => void
  onSearch?: (searchTerm: string) => void
  onClickShowMore?: () => void
}

export interface DefaultFilterGroupProps extends BaseFilterGroupProps {
  type: "default" | "color"
  items: FilterItem[]
}

export interface SizeFilterGroupProps extends BaseFilterGroupProps {
  type: "size"
  sizeOptions: SizeOption[]
  sizeTypes: string[]
}

export interface PriceRangeFilterGroupProps extends BaseFilterGroupProps {
  type: "priceRange"
  currency: string
  onPriceChange: (min: number, max: number) => void
}

export type FilterGroupProps = (
  | DefaultFilterGroupProps
  | SizeFilterGroupProps
  | PriceRangeFilterGroupProps
) & {
  className?: string
}

export interface FilterGroupHandle {
  forceUpdate: () => void
}

export function isDefaultFilterGroupProps(
  props: FilterGroupProps,
): props is DefaultFilterGroupProps {
  return props.type === "default" || props.type === "color"
}

export function isSizeFilterGroupProps(
  props: FilterGroupProps,
): props is SizeFilterGroupProps {
  return props.type === "size"
}

export function isPriceRangeFilterGroupProps(
  props: FilterGroupProps,
): props is PriceRangeFilterGroupProps {
  return props.type === "priceRange"
}
export interface FilterLogic {
  isOpen: boolean
  showAll: boolean
  searchTerm: string
  selectedItems: string[]
  minPrice: string
  maxPrice: string
  filteredItems: FilterItem[]
  displayedItems: FilterItem[]
  currentSizeType: string
  toggleOpen: () => void
  toggleShowAll: () => void
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleItemClick: (itemName: string) => void
  handlePriceChange: (min: string, max: string) => void
  setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>
  setMinPrice: React.Dispatch<React.SetStateAction<string>>
  setMaxPrice: React.Dispatch<React.SetStateAction<string>>
  setCurrentSizeType: React.Dispatch<React.SetStateAction<string>>
  parseParamsFromURL: () => void
}
