import { useState } from "react"

interface Window {
  MidtransNew3ds: {
    authenticate: (redirectUrl: string, options?: any) => Promise<any>
  }
}

const useAuthenticate3Ds = ({
  onSuccess,
  onPerformAuth,
  onPending,
  onFailure,
}: {
  onSuccess?: (txId: number) => void
  onPerformAuth: (url: string) => void
  onPending?: (txId: number) => void
  onFailure?: (response: any, txId: number) => void
}) => {
  const [isPending, setIsPending] = useState(false)

  const submitAuthenticate3Ds = async (redirectUrl: string, txId: number) => {
    setIsPending(true)
    const options = {
      performAuthentication(url: string) {
        onPerformAuth(url)
      },
      onSuccess() {
        onSuccess?.(txId)
        setIsPending(false)
      },
      onFailure(response: any) {
        // eslint-disable-next-line no-console
        console.error("response:", response)
        setIsPending(false)
        onFailure?.(response, txId)
      },
      onPending() {
        onPending?.(txId)
        setIsPending(false)
      },
    }

    const w = window as unknown as Window
    w.MidtransNew3ds.authenticate(redirectUrl, options)
  }

  return {
    isPending,
    submitAuthenticate3Ds,
  }
}

export default useAuthenticate3Ds
