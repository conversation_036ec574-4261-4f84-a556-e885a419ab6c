import { useQuery } from "@tanstack/react-query"

import { GetVouchers } from "@application/usecases/getVouchers"
import { VoucherApiRepository } from "@infrastructure/repositories/voucherApiRepository"

export const useCheckoutVoucher = () => {
  const voucherRepository = new VoucherApiRepository()

  const { data } = useQuery({
    queryKey: ["getVoucher"],
    queryFn: async () => {
      const usecase = new GetVouchers(voucherRepository)
      const result = await usecase.getAllVoucherActive()
      return result.content ?? []
    },
  })

  return {
    data,
  }
}
