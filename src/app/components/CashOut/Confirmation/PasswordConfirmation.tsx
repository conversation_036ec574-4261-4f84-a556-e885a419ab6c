import { Button, IconKeyBulk, Space, Text } from "@kickavenue/ui/components"
import { useFormContext } from "react-hook-form"
import { useMutation } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { snakeCase } from "lodash"

import PasswordInput from "@app/components/InputPassword"
import useBalanceType from "@app/hooks/useBalanceType"
import { getApiErrorMessage } from "@utils/network"
import { CashOutAPIRepository } from "@infrastructure/repositories/cashOutApiRepository"
import { CashOutPayload } from "types/cashOut.type"
import { PageRouteConstant } from "@constants/pageRoute.constant"
import { objectToQueryParams } from "@utils/query.utils"
import { EBalanceType } from "types/balance.type"
import useGetCurrentBalanceAndFee from "@hooks/useGetCurrentBalanceAndFee"
import Spinner from "@components/shared/Spinner"
import { convertText } from "@utils/misc"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"
import useGetAllBankInfo from "@app/hooks/useGetAllBankInfo"

// eslint-disable-next-line max-lines-per-function
const PasswordConfirmation = () => {
  const router = useRouter()

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()
  const amount = Number(watch("amount"))
  const bankId = watch("bankId")
  const password = watch("password")

  const { isKickCredit } = useBalanceType()
  const { currentFeeNumber } = useGetCurrentBalanceAndFee()
  const { member } = useMemberStore()

  const { data: allBankInfo } = useGetAllBankInfo({
    ids: bankId ? [bankId] : [],
    page: 0,
    pageSize: 5,
  })

  const getBankInfo = (bankId: number) => {
    return allBankInfo?.content.find((bank) => bank.id === bankId)
  }

  const {
    mutate: submitCashOut,
    isPending,
    error,
  } = useMutation({
    mutationFn: async (password: string) => {
      const repository = new CashOutAPIRepository()
      const payload: CashOutPayload = {
        amount: isKickCredit ? amount : amount - currentFeeNumber,
        balanceType: isKickCredit
          ? EBalanceType.KickCredit
          : EBalanceType.SellerCredit,
        bankAccountId: bankId as number,
        password,
      }
      const res = await repository.create(payload)
      return res
    },
    onSuccess: (data) => {
      const fullPath = objectToQueryParams({
        path: PageRouteConstant.CASH_OUT_FINISH_REQUESTED,
        query: {
          balanceType: isKickCredit
            ? convertText(EBalanceType.KickCredit).toKebabCase()
            : convertText(EBalanceType.SellerCredit).toKebabCase(),
        },
      })
      event({
        action: "user_cash_out_success",
        params: {
          [snakeCase("disbursement_no")]: data.invoiceNumber,
          [snakeCase("payment_method")]: getBankInfo(bankId)?.bankName,
          [snakeCase("top_up_amount")]: data?.amount,
          [snakeCase("top_up_type")]: isKickCredit
            ? EBalanceType.KickCredit
            : EBalanceType.SellerCredit,
        },
        userId: member?.id,
      })
      router.push(fullPath)
    },
  })

  const handleOnFormValid = () => {
    submitCashOut(password)

    event({
      action: "user_cash_out_started",
      params: {
        [snakeCase("payment_method")]: getBankInfo(bankId)?.bankName,
        [snakeCase("top_up_amount")]: isKickCredit
          ? amount
          : amount - currentFeeNumber,
        [snakeCase("top_up_type")]: isKickCredit
          ? EBalanceType.KickCredit
          : EBalanceType.SellerCredit,
      },
      userId: member?.id,
    })
  }

  const textButton = isPending ? <Spinner /> : "Confirm"
  const isDisabled = isPending || !password

  return (
    <div className="min-h-screen bg-white py-lg md:flex md:justify-center">
      <div className="m-auto w-full max-w-[400px]">
        <div className="w-full p-base text-center text-heading-4 font-bold">
          Password Confirmation
        </div>

        <form onSubmit={handleSubmit(handleOnFormValid)}>
          <div className="flex flex-col pb-sm">
            <div className="flex justify-center">
              <IconKeyBulk width={64} height={64} />
            </div>
            <Space size="lg" direction="y" type="margin" />

            <PasswordInput
              value={password}
              onChange={(value) => setValue("password", value)}
              showRequirements={false}
              autoComplete="current-password"
              className="[&>div>div>svg]:cursor-pointer [&>div>label]:hidden"
            />

            {(error || Boolean(errors.password?.message)) && (
              <Text size="sm" state="danger" type="regular" className="mb-2">
                {error instanceof Error &&
                  getApiErrorMessage(error, "An error occurred")}
                {errors.password?.message && String(errors.password?.message)}
              </Text>
            )}
          </div>

          <Button
            type="submit"
            size="lg"
            variant="primary"
            className="!w-full"
            disabled={isDisabled}
          >
            {textButton}
          </Button>
        </form>
      </div>
    </div>
  )
}

export default PasswordConfirmation
