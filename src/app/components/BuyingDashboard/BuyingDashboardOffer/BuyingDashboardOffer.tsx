import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

import useURLQuery from "@app/hooks/useUrlQuery"
import { MiscConstant } from "@constants/misc"
import DashboardTableToolbar from "@components/shared/DashboardTableToolbar"
import FilterDashboard from "@components/FilterDashboard"

import { EBuyingDashboardTab } from "../buyingDashboard.type"
import { BuyingDashboardConstant } from "../buyingDashboard.contant"
import useBuyingDashboardOfferSearchParams from "../hooks/useBuyingDashboardOfferSearchParams"

import BuyingDashboardOfferTable from "./BuyingDashboardOfferTable"
import BuyingDashboardOfferBulkActions from "./BuyingDashboardOfferBulkActions"
import BuyingDashboardOfferUpdatePriceModal from "./BuyingDashboardOfferUpdatePriceModal"
import BuyingDashboardOfferUpdateExpiryModal from "./BuyingDashboardOfferUpdateExpiryModal"
import BuyingDashboardOfferDeleteModal from "./BuyingDashboardOfferDeleteModal"
import BuyingDashboardOfferDeleteConfirm from "./BuyingDashboardOfferDeleteConfirm"
import BuyingDashboardOfferDetailModal from "./BuyingDashboardOfferDetailModal"

const { TAB, PAGE, PAGE_SIZE } = BuyingDashboardConstant.FILTER_FIELDS

const { Offers } = EBuyingDashboardTab

const BuyingDashboardOffer = () => {
  const searchParams = useSearchParams()
  const { handleBulkChangeQuery } = useURLQuery()
  const [openFilter, setOpenFilter] = useState(false)
  const selectedTab = searchParams.get(TAB) || Offers

  useBuyingDashboardOfferSearchParams()

  useEffect(() => {
    if (searchParams.toString()) return
    handleBulkChangeQuery({
      [PAGE]: MiscConstant.PAGING_DEFAULT.PAGE,
      [PAGE_SIZE]: MiscConstant.PAGING_DEFAULT.PAGE_SIZE,
    })
  }, [handleBulkChangeQuery, searchParams])

  return (
    <>
      <DashboardTableToolbar onClick={() => setOpenFilter(true)} />
      <BuyingDashboardOfferTable />
      <BuyingDashboardOfferBulkActions />
      <BuyingDashboardOfferUpdatePriceModal />
      <BuyingDashboardOfferUpdateExpiryModal />
      <BuyingDashboardOfferDeleteModal />
      <BuyingDashboardOfferDeleteConfirm />
      <BuyingDashboardOfferDetailModal />
      <FilterDashboard
        openFilterOfferPrice={openFilter}
        setOpenFilterOfferPrice={setOpenFilter}
        selectedTab={selectedTab}
      />
    </>
  )
}

export default BuyingDashboardOffer
