import ButtonAppStore from "@kickavenue/ui/components/Button/ButtonAppStore"
import Space from "@kickavenue/ui/components/Space"
import ButtonGooglePlay from "@kickavenue/ui/components/Button/ButtonGooglePlay"
import { QRCode } from "@kickavenue/ui/components/icons"

import FooterTitle from "./FooterTitle"
import FooterMenuLink from "./FooterMenuLink"
import {
  getAboutTextLink,
  getHelpTextLink,
  getProductsTextLink,
  getSocialMediaTextLink,
} from "./footer.utils"

const FooterLinkSection = () => (
  <>
    <div className="col-span-4">
      <FooterTitle text="Download Kick Avenue App" />
      <div className="flex w-full gap-sm">
        <div className="flex flex-col items-start">
          <QRCode className="h-auto w-full" />
        </div>
        <div className="flex flex-col items-start">
          <ButtonAppStore url="https://apps.apple.com/app/kick-avenue-shop-hype-here/id1478394222" />
          <Space size="xs" direction="y" type="margin" />
          <ButtonGooglePlay url="https://play.google.com/store/apps/details?id=com.kickavenue.androidshop" />
        </div>
      </div>
    </div>
    <div className="col-span-2">
      <FooterTitle text="Products" />
      {getProductsTextLink().map((item) => (
        <FooterMenuLink key={item.text} {...item} />
      ))}
    </div>
    <div className="col-span-2">
      <FooterTitle text="About" />
      {getAboutTextLink().map((item) => (
        <FooterMenuLink key={item.text} {...item} />
      ))}
    </div>
    <div className="col-span-2">
      <FooterTitle text="Support" />
      {getHelpTextLink().map((item) => (
        <FooterMenuLink key={item.text} {...item} />
      ))}
    </div>
    <div className="col-span-2">
      <FooterTitle text="Social Media" />
      {getSocialMediaTextLink().map((item) => (
        <FooterMenuLink key={item.text} {...item} />
      ))}
    </div>
  </>
)

export default FooterLinkSection
