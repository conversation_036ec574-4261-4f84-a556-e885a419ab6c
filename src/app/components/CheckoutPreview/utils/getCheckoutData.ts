import { MiscConstant } from "@constants/misc"
import { TCheckoutPreviewData } from "stores/useCheckoutPreviewStore"

const { CHECKOUT_PREVIEW_STORAGE_KEY } = MiscConstant

export const getCheckoutData = (): TCheckoutPreviewData | null => {
  const storedData = localStorage.getItem(CHECKOUT_PREVIEW_STORAGE_KEY)
  if (!storedData) return null

  try {
    const { itemId, sizeId, shippingText } = JSON.parse(storedData)
    return {
      itemId,
      sizeId,
      shippingText,
    }
  } catch (error) {
    return null
  }
}
