"use client"

import useChangePage from "@app/hooks/useChangePage"
import SearchInput, { SearchInputProps } from "@components/shared/SearchInput"
import { PageRouteConstant } from "@constants/pageRoute.constant"

interface HelpCenterSearchInputProps extends SearchInputProps {}

const HelpCenterSearchInput = (props: HelpCenterSearchInputProps) => {
  const { goToPage } = useChangePage()
  const handleEnterPress = (value: string) => {
    goToPage(`${PageRouteConstant.HELP_CENTER_SEARCH}`, {
      keyword: value,
    })
  }
  return (
    <SearchInput
      placeholder="How can we help?"
      size="lg"
      onEnterPress={handleEnterPress}
      className="max-w-[400px]"
      {...props}
    />
  )
}

export default HelpCenterSearchInput
