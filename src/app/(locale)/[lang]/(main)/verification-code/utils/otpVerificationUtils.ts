import { verify } from "@infrastructure/api/verifyOtp"
import { userApi } from "@infrastructure/api/userApi"
import { event } from "@lib/gtag"

interface OtpValidationResult {
  success: boolean
  error?: string
  smsError?: string
  emailError?: string
  tokenActivation?: string
}

export const validateOtpLength = (
  emailOtp: string,
  phoneOtp: string,
): OtpValidationResult => {
  if (emailOtp.length !== 6 || phoneOtp.length !== 6) {
    return {
      success: false,
      smsError: "Please enter a valid SMS OTP",
      emailError: "Please enter a valid Email OTP",
    }
  }
  return { success: true }
}

export const validateStoredData = (
  storedData: string | null,
): OtpValidationResult => {
  if (!storedData) {
    return {
      success: false,
      smsError: "Registration data not found",
      emailError: "Registration data not found",
    }
  }

  const { requestTimer } = JSON.parse(storedData)
  if (!requestTimer) {
    return {
      success: false,
      smsError: "Invalid registration data",
      emailError: "Invalid registration data",
    }
  }

  const requestTime = new Date(requestTimer).getTime()
  const currentTime = new Date().getTime()
  const timeDiff = (currentTime - requestTime) / 1000 / 60

  if (timeDiff > 5) {
    return {
      success: false,
      smsError: "OTP has expired. Please request a new one",
      emailError: "OTP has expired. Please request a new one",
    }
  }

  return { success: true }
}

export const verifyOtpCodes = async (
  emailOtp: string,
  phoneOtp: string,
): Promise<OtpValidationResult> => {
  try {
    const response = await verify([
      { code: phoneOtp, type: "SMS" },
      { code: emailOtp, type: "EMAIL" },
    ])

    if (response.Code === 0 && response.Status === "success") {
      const smsOtp = response.Data.find((item: any) => item.type === "SMS")
      const emailOtp = response.Data.find((item: any) => item.type === "EMAIL")

      if (smsOtp?.is_valid && emailOtp?.is_valid) {
        return {
          success: true,
          tokenActivation: smsOtp.token_activation || emailOtp.token_activation,
        }
      }

      const smsError =
        smsOtp?.error || (smsOtp?.is_valid ? undefined : "Invalid SMS OTP")
      const emailError =
        emailOtp?.error ||
        (emailOtp?.is_valid ? undefined : "Invalid Email OTP")

      return {
        success: false,
        smsError,
        emailError,
      }
    }

    return {
      success: false,
      error: response.Message || "Verification failed",
    }
  } catch (err) {
    const errorMessage =
      err instanceof Error
        ? err.message
        : "An error occurred during verification"
    return {
      success: false,
      error: errorMessage,
    }
  }
}

export const registerUser = async (
  registrationData: any,
  tokenActivation: string,
) => {
  try {
    const response = await userApi.register({
      countryId: registrationData.countryId,
      createdSource: registrationData.createdSource || "WEB",
      email: registrationData.email,
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      password: registrationData.password,
      passwordConfirmation: registrationData.passwordConfirmation,
      phoneNumber: registrationData.phoneNumber,
      tokenActivation,
    })

    if (response.Code === 0 && response.Status === "success") {
      event({
        action: "user_verification",
        params: {
          email: response.Data.email,
        },
        userId: response.Data.id,
      })
      return {
        success: true,
      }
    }

    return {
      success: false,
      error: response.Message || "Registration failed",
    }
  } catch (err) {
    return {
      success: false,
      error: err instanceof Error ? err.message : "Registration failed",
    }
  }
}
