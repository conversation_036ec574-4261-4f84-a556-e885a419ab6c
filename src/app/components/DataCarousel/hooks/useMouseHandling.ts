import { useCallback, useEffect, useState } from "react"

const useMouseHandling = (
  scrollContainerRef: React.RefObject<HTMLDivElement>,
) => {
  const [isMouseDragging, setIsMouseDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!scrollContainerRef.current) return
      setIsMouseDragging(true)
      setStartX(e.pageX - scrollContainerRef.current.offsetLeft)
      setScrollLeft(scrollContainerRef.current.scrollLeft)
    },
    [scrollContainerRef],
  )

  const handleMouseMove = useCallback(
    (e: React.MouseEvent | MouseEvent) => {
      if (!isMouseDragging || !scrollContainerRef.current) return
      e.preventDefault()
      const x = e.pageX - scrollContainerRef.current.offsetLeft
      const walk = (x - startX) * 2
      scrollContainerRef.current.scrollLeft = scrollLeft - walk
    },
    [isMouseDragging, scrollLeft, startX, scrollContainerRef],
  )

  const handleMouseUp = useCallback(() => {
    setIsMouseDragging(false)
  }, [])

  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const domMouseMove = (e: MouseEvent) => handleMouseMove(e)
    const domMouseUp = () => handleMouseUp()

    if (isMouseDragging) {
      document.addEventListener("mousemove", domMouseMove)
      document.addEventListener("mouseup", domMouseUp)
    }

    return () => {
      document.removeEventListener("mousemove", domMouseMove)
      document.removeEventListener("mouseup", domMouseUp)
    }
  }, [handleMouseMove, handleMouseUp, isMouseDragging, scrollContainerRef])

  return {
    handleMouseDown,
    isMouseDragging,
  }
}

export default useMouseHandling
